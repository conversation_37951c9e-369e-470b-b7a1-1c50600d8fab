#!/usr/bin/env python3
from PIL import Image, ImageDraw
import os

def create_app_icon():
    # Create the main 1024x1024 icon
    size = 1024
    img = Image.new('RGB', (size, size), 'white')
    draw = ImageDraw.Draw(img)
    
    # Define colors
    blue = '#1f2937'  # Dark blue
    
    # Create an upward trending line graph
    # Define points for the line (x, y coordinates)
    points = [
        (200, 700),   # Start point (bottom left area)
        (350, 600),   # Second point
        (500, 450),   # Third point  
        (650, 350),   # Fourth point
        (800, 250)    # End point (top right area)
    ]
    
    # Draw the line with thickness
    for i in range(len(points) - 1):
        draw.line([points[i], points[i + 1]], fill=blue, width=25)
    
    # Add small circles at each point for a polished look
    for point in points:
        x, y = point
        draw.ellipse([x-15, y-15, x+15, y+15], fill=blue)
    
    # Save the main icon
    img.save('AppIcon-1024x1024.png')
    
    # Create all required sizes
    sizes = [
        (40, '<EMAIL>'),
        (60, '<EMAIL>'),
        (58, '<EMAIL>'),
        (87, '<EMAIL>'),
        (80, '<EMAIL>'),
        (120, '<EMAIL>'),
        (120, '<EMAIL>'),
        (180, '<EMAIL>')
    ]
    
    for size_px, filename in sizes:
        resized = img.resize((size_px, size_px), Image.Resampling.LANCZOS)
        resized.save(filename)
    
    print("✅ App icons created successfully!")
    print("Files created:")
    print("- AppIcon-1024x1024.png")
    for _, filename in sizes:
        print(f"- {filename}")

if __name__ == "__main__":
    create_app_icon() 