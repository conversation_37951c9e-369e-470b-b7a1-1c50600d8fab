# Open Items - Investment Tracker App

## 🐛 Known Issues

### 1. RSU Data Persistence Issue ✅ **RESOLVED**
**Priority**: ~~HIGH - MAJOR BLOCKER~~ → **RESOLVED**  
**Affected Views**: RSU module  
**Description**: ~~RSU data is not persisting between app launches/reinstalls~~ → **FIXED**

**Resolution**:
The persistence issue was resolved when implementing the live exchange rate feature. The combination of:
- Updated PortfolioViewModel async initialization with ExchangeRateService
- Modified RSU model structure with proper stored properties
- Enhanced timing of data loading/saving operations

**⚠️ CAUTION**: 
- **Requires thorough testing** across multiple app sessions and device restarts
- **Test both simulator and physical device** to ensure consistency
- **Verify data integrity** after multiple add/edit/delete operations
- **Monitor for any regression** in future updates

**Status**: ✅ **RESOLVED** - RSU data now persists correctly between app launches
**Verification**: User confirmed RSU data survives app reinstalls and relaunches

---

### 2. Edit Form Data Retention Issue
**Priority**: Medium  
**Affected Views**: EPF, NPS, Fixed Deposits, Crypto, Gold  
**Description**: When clicking "Edit" on existing entries, the edit form appears empty instead of pre-populating with current values.

**Current Behavior**:
- User clicks "Edit" on an existing EPF/NPS/Fixed Deposit/Crypto/Gold entry
- Edit form opens with all fields blank/reset to default values
- User has to re-enter all information instead of modifying existing values

**Expected Behavior**:
- Edit form should pre-populate with current values from the selected entry
- User should be able to modify existing values and save changes

**Technical Notes**:
- Issue persists despite implementing custom initializers with State initialization
- Shared PortfolioViewModel architecture is working correctly
- May be related to SwiftUI sheet presentation timing or State management
- **Recently confirmed**: Crypto module also affected - edit form opens but values not pre-filled
- **Latest**: Gold module also affected - edit form appears empty when editing existing gold investments

**Workaround**: Users can view current values in the main list and manually re-enter them in the edit form.

---

### 3. Investment Date Field Semantics Issue
**Priority**: Medium  
**Affected Views**: Mutual Funds, potentially other investment types  
**Description**: The "Investment Date" field doesn't make semantic sense for aggregated/averaged investment data.

**Current Behavior**:
- All investment forms use "Investment Date" field
- For mutual funds, users often enter aggregated data (total units, average NAV)
- "Investment Date" becomes meaningless when representing multiple purchase dates

**Expected Behavior**:
- For aggregated data entries: Use "Last Updated" instead of "Investment Date"
- For single transaction entries: Keep "Investment Date"
- Consider allowing users to choose between single transaction vs aggregated data entry modes

**Affected Investment Types**:
- **Mutual Funds**: Often aggregated data → should use "Last Updated"
- **Stocks**: Could be either single purchase or aggregated → needs review
- **EPF/NPS**: Continuous contributions → "Last Updated" makes more sense
- **Fixed Deposits**: Single investment → "Investment Date" is appropriate
- **RSU**: Vesting dates → current approach is appropriate

**Technical Notes**:
- May need to update data models to support both field types
- UI forms would need conditional rendering based on investment type
- Consider adding metadata to distinguish between single vs aggregated entries

---

### 4. Mutual Fund Search Coverage Issue
**Priority**: Low  
**Affected Views**: Mutual Funds  
**Description**: Some mutual funds are missing from the search results despite having valid scheme codes and working NAV data.

**Current Behavior**:
- Specific funds like "Motilal Oswal Midcap 150 Index Fund" don't appear in search results
- Manual entry of the scheme code successfully fetches NAV data
- Search API may have incomplete fund database or different naming conventions

**Expected Behavior**:
- All funds with valid scheme codes should be discoverable through search
- Search should handle variations in fund names and aliases

**Technical Notes**:
- NAV fetching API works correctly with scheme codes
- Issue is likely with the search/discovery API data completeness
- May need to investigate alternative search APIs or data sources
- Could implement fuzzy search or alias mapping for common fund variations

**Workaround**: Users can manually enter the scheme code if they know it, and NAV fetching will work correctly.

---

### 5. Hardcoded Values Cleanup
**Priority**: Medium  
**Affected Views**: All modules  
**Description**: The project contains hardcoded values throughout that should be centralized and configurable.

**Current Issues**:
- API endpoints hardcoded in service classes
- Exchange names and symbols hardcoded in various places
- Currency symbols and formatting rules scattered across views
- Default values and constants not centralized

**Expected Improvements**:
- Create centralized configuration/constants files
- Move API endpoints to configuration
- Centralize currency formatting and exchange mappings
- Make default values configurable and maintainable

**Technical Notes**:
- Affects maintainability and extensibility
- Makes it difficult to support multiple regions/currencies
- Complicates testing with different configurations
- Should be addressed before adding new features

---

### 6. Crypto Implementation Tight Coupling to CoinDCX
**Priority**: Medium  
**Affected Views**: Crypto module  
**Description**: Current crypto implementation is tightly coupled to CoinDCX exchange, making it difficult to support other exchanges or switch providers.

**Current Issues**:
- CoinDCX API endpoints hardcoded in CryptoPriceService
- Symbol format (SYMBOLINR) specific to CoinDCX
- Price fetching logic assumes CoinDCX response structure
- No abstraction layer for different crypto exchanges

**Expected Improvements**:
- Create generic crypto exchange protocol/interface
- Abstract symbol formatting and price fetching logic
- Support multiple crypto exchanges (Binance, WazirX, etc.)
- Allow users to choose preferred exchange for price data

**Technical Notes**:
- Should implement exchange adapter pattern
- Need to handle different API rate limits and response formats
- Consider fallback mechanisms if primary exchange is unavailable
- May require UI changes to let users select exchange preferences

**Impact**: Currently functional but limits future extensibility and user choice.

---

### 7. Inconsistent UI/UX Patterns Across Investment Modules
**Priority**: Medium  
**Affected Views**: All investment modules (Stocks, Mutual Funds, Crypto, Gold, EPF, NPS, Fixed Deposits, RSU)  
**Description**: Investment modules use different card layouts, terminology, and interaction patterns, creating an inconsistent user experience across the app.

**Current Issues**:

**Card Layout Inconsistencies**:
- Different card designs and information hierarchy across modules
- Inconsistent preview/collapse functionality for detailed data
- Varying spacing, typography, and visual styling
- Different approaches to displaying key metrics and P&L information

**Terminology Inconsistencies**:
- "Quantity" vs "Units" vs "Amount" used interchangeably for similar concepts
- Different field labels for similar data (e.g., investment amount, purchase value)
- Inconsistent date field naming and semantics
- Mixed currency formatting and display patterns

**Interaction Pattern Inconsistencies**:
- Edit and delete actions implemented differently across modules
- Some use swipe actions, others use button menus, some use context menus
- Different icon choices for similar actions (edit, delete, add)
- Inconsistent navigation patterns and sheet presentations
- Varying approaches to form validation and error handling

**Expected Improvements**:
- Standardize card component design across all investment types
- Create unified terminology dictionary and enforce consistent usage
- Implement consistent interaction patterns for CRUD operations
- Develop reusable UI components for common investment data display
- Establish design system guidelines for investment modules

**Technical Notes**:
- Should create shared UI component library for investment cards
- Need design system documentation for consistent implementation
- Consider creating base protocols/interfaces for investment data display
- May require refactoring existing views to use standardized components
- Should implement consistent theming and styling approach

**Impact**: Affects user experience consistency and makes the app feel fragmented rather than cohesive.

---

## 🚀 Future Enhancements

### 1. Data Export/Import
- Export portfolio data to CSV/Excel
- Import transactions from bank statements
- Backup and restore functionality

### 2. Advanced Analytics
- Portfolio performance charts
- Asset allocation pie charts
- Historical performance tracking
- Risk analysis metrics

### 3. Notifications & Alerts
- FD maturity date reminders
- Portfolio milestone notifications
- Market update alerts

### 4. Additional Investment Types
- Bonds and Debentures
- International Stocks
- Commodities beyond Gold
- Real Estate Investment Trusts (REITs)

### 5. Integration Features
- Bank account integration
- Real-time market data
- Tax calculation helpers
- Financial goal tracking

---

## 📝 Code Quality Improvements

### 1. Unit Tests
- Add comprehensive unit tests for all models
- Test XIRR calculations with various scenarios
- Validate currency formatting edge cases

### 2. Error Handling
- Improve error messages and user feedback
- Add network error handling for future integrations
- Implement data validation with better user guidance

### 3. Performance Optimization
- Optimize large portfolio rendering
- Implement lazy loading for historical data
- Cache calculations for better performance

---

## 🎨 UI/UX Enhancements

### 1. Dark Mode Support
- Ensure all views work well in dark mode
- Test color schemes and contrast

### 2. Accessibility
- Add VoiceOver support
- Improve keyboard navigation
- Ensure proper color contrast ratios

### 3. iPad Support
- Optimize layouts for larger screens
- Consider split-view functionality

---

**Last Updated**: June 28, 2025  
**App Version**: 1.0 (Development) 