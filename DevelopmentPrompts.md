# iOS Investment Tracker App - Development Prompts

## Phase 1: Project Setup & Foundation

### 1. Initial Project Setup
**Prompt:**
```
Create a new iOS project structure for an investment tracking app called 'InvestmentTracker'. Set up the basic Xcode project with SwiftUI, include proper folder structure for Models, Views, ViewModels, Services, and Utils. Also create a basic app structure with navigation.
```

### 2. Core Data Models
**Prompt:**
```
Create Swift data models for all investment asset classes based on the requirements: Stock, MutualFund, Crypto, FixedDeposit, EPF, NPS, RSU, and Gold. Include properties for manual data entry, dates, amounts in INR, and P&L calculations. Make them Codable for data persistence.
```

### 3. Currency and Calculation Utilities
**Prompt:**
```
Create utility classes for INR currency formatting, XIRR calculation functions, and P&L calculation methods. Include helper functions for date handling and percentage calculations that will be used across different asset classes.
```

## Phase 2: Basic UI Framework

### 4. Main Dashboard UI
**Prompt:**
```
Create the main dashboard UI using SwiftUI with a portfolio overview showing total value, overall P&L, and diversification breakdown as a pie chart. Include navigation to individual asset class screens and use modern iOS design patterns.
```

### 5. Navigation Structure
**Prompt:**
```
Implement tab-based navigation for different asset classes (Stocks, Mutual Funds, Crypto, FDs, EPF, NPS, RSU, Gold) with proper tab icons and a main Portfolio tab. Include navigation controllers and proper screen transitions.
```

## Phase 3: Individual Asset Class Implementation

### 6. Stocks Module
**Prompt:**
```
Create the Stocks module with SwiftUI views for listing stocks, adding new stock entries manually, editing existing entries, and showing individual stock P&L. Include forms for stock symbol, quantity, purchase price, purchase date, and current value input.
```

### 7. Mutual Funds Module
**Prompt:**
```
Create the Mutual Funds module similar to stocks, with views for listing funds, manual entry forms for fund name, units, NAV, purchase date, and current value. Include P&L calculations and XIRR display for each fund.
```

### 8. Crypto Module
**Prompt:**
```
Create the Crypto module with support for manual entry of cryptocurrency holdings including coin name, quantity, purchase price, purchase date, and current value. Include P&L calculations specific to crypto investments.
```

### 9. Fixed Deposits Module
**Prompt:**
```
Create the Fixed Deposits module with forms to enter bank name, FD amount, start date, end date, interest rate, and maturity amount. Calculate and display current value, accrued interest, and XIRR.
```

### 10. EPF Module
**Prompt:**
```
Create the EPF module with simple manual entry for invested amount and current balance. Include functionality to add entries over time (monthly/quarterly) and calculate basic returns without complex interest breakdown.
```

### 11. NPS Module
**Prompt:**
```
Create the NPS module similar to EPF, with manual entry for invested amount and current NAV. Include XIRR calculation based on investment timeline and current value.
```

### 12. RSU Module
**Prompt:**
```
Create the RSU module with fields for number of stocks, unit value, total value, vesting dates, and company name. Include calculations for current worth and unrealized gains.
```

### 13. Gold Module
**Prompt:**
```
Create the Gold module with manual entry for gold rate, grams purchased, purchase date, and current gold rate. Calculate current value and P&L based on current market rates.
```

## Phase 4: Core Features Integration

### 14. Portfolio Analytics
**Prompt:**
```
Implement portfolio diversification breakdown showing percentage allocation across different asset classes. Create pie charts and summary cards showing total portfolio value, overall P&L, and asset-wise distribution.
```

### 15. XIRR Implementation
**Prompt:**
```
Implement comprehensive XIRR calculation across all asset classes and for the overall portfolio. Create functions to handle cash flows, investment dates, and current values to provide accurate return calculations.
```

### 16. Data Persistence
**Prompt:**
```
Implement Core Data or UserDefaults-based local storage for all investment data. Ensure data persistence across app launches, with proper data migration and backup capabilities.
```

## Phase 5: User Experience & Polish

### 17. Add/Edit/Delete Functionality
**Prompt:**
```
Implement comprehensive CRUD operations for all asset classes with intuitive SwiftUI forms, validation, confirmation dialogs for deletions, and proper error handling.
```

### 18. Data Import/Export
**Prompt:**
```
Create functionality to export portfolio data to CSV/JSON format and import data from CSV files for bulk entry. Include proper file handling and data validation.
```

### 19. UI Polish & Animations
**Prompt:**
```
Add smooth animations, loading states, empty states, and professional styling to all screens. Implement pull-to-refresh, swipe actions, and modern iOS design elements.
```

## Phase 6: Testing & Deployment

### 20. Testing Setup
**Prompt:**
```
Set up unit tests for calculation functions, data models, and business logic. Create UI tests for critical user flows and implement proper test data setup.
```

### 21. Local Testing & Installation
**Prompt:**
```
Guide through testing the app on physical device using Xcode, including certificate setup, device registration, and troubleshooting common iOS development issues for personal use without App Store.
```

### 22. Performance Optimization
**Prompt:**
```
Optimize app performance, implement proper memory management, add loading indicators for calculations, and ensure smooth scrolling for large data sets.
```

## Phase 7: Future Enhancements Setup

### 23. API Integration Framework
**Prompt:**
```
Create a networking layer and service architecture to prepare for future broker API integrations (Zerodha, Binance, etc.). Set up proper API key management and data synchronization structure.
```

### 24. Enhanced Analytics Preparation
**Prompt:**
```
Set up the foundation for historical performance tracking, goal setting features, and advanced portfolio analytics that can be added in future iterations.
```

### 25. Data Backup & Restore System
**Prompt:**
```
Implement a comprehensive backup and restore system for portfolio data. Create functionality to export all investment data (stocks, FDs, crypto, etc.) to JSON files that can be saved to Files app or shared via AirDrop/email. Include import functionality to restore data from backup files. Add automatic backup reminders and manual backup triggers in the app settings. This ensures data safety during app rebuilds or device changes.

Features to implement:
- Export all portfolio data to timestamped JSON backup files
- Import data from backup files with validation and merge options
- Save backups to iOS Files app for easy access
- Share backups via AirDrop, email, or cloud storage
- Backup validation and data integrity checks
- Settings page with backup management options
- Automatic backup prompts (weekly/monthly reminders)
- Restore options: Replace all data or merge with existing data
```

## Development Notes

- Execute prompts in sequential order for best results
- Test functionality after each major phase before proceeding
- Each phase builds upon the previous one
- Focus on getting basic functionality working before adding complexity
- The app will be developed incrementally, allowing for testing at each stage

## Key Considerations

- All currency values should be in INR
- Focus on manual data entry initially
- Prepare architecture for future API integrations
- Ensure proper data persistence and user experience
- Design for personal use without App Store requirements initially 