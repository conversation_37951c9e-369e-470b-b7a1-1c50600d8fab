{"": {"diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-master.dia", "emit-module-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-master.swiftdeps"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/ContentView.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/InvestmentTrackerApp.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/Investment.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/SampleData.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Services/DataManager.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/CurrencyFormatter.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/DateHelper.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/PercentageCalculator.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/ProfitLossCalculator.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/XIRRCalculator.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/ViewModels/PortfolioViewModel.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/CryptoView.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/MutualFundsView.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/OtherInvestmentsView.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/PortfolioView.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView~partial.swiftmodule"}, "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/StocksView.swift": {"const-values": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.swiftconstvalues", "dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.d", "diagnostics": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.dia", "index-unit-output-path": "/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.o", "llvm-bc": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.bc", "object": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.o", "swift-dependencies": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.swiftdeps", "swiftmodule": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView~partial.swiftmodule"}}