{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.personal.InvestmentTracker", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "4b154dc3bd1054789ca4de7b9572d5a8a89d70ec782d5110cbbde0f073befbf3", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.personal.InvestmentTracker", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "4b154dc3bd1054789ca4de7b9572d5a801bbaa87e647fb2491bf0524ffd118af", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "4b154dc3bd1054789ca4de7b9572d5a878c64aaf120eb90a7d3eb70642a039a1", "guid": "4b154dc3bd1054789ca4de7b9572d5a8298a48c48731210fa873e33971611f7e"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a87904764540c8ce8c1ac842ff6a869bfc", "guid": "4b154dc3bd1054789ca4de7b9572d5a828f4c3147b7db253c222db57a8ae31ba"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a8274e999f88cd3799f199b7a002a96004", "guid": "4b154dc3bd1054789ca4de7b9572d5a865dcff1a6c5fa67d5e7f74749885ecac"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a82b20199f89438b8e42d6552fc9ef9ab4", "guid": "4b154dc3bd1054789ca4de7b9572d5a81fef6ee9787c3ca6bd50752ad0c4d306"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a825f72d22f2a6430d3aa76d182913d9be", "guid": "4b154dc3bd1054789ca4de7b9572d5a8b38ff94b2ff2eb4a7181b257cc72713f"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a8d3339fb35e090a10c33f89d43e59c668", "guid": "4b154dc3bd1054789ca4de7b9572d5a84782a267f1c02f1fd2707817887e3980"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a8a647ad2d678df12604390b4a30f0093c", "guid": "4b154dc3bd1054789ca4de7b9572d5a87d6f29060b334395068a443e4dc57bdc"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a823bc32356bc9cefa731e8a90efc16dc3", "guid": "4b154dc3bd1054789ca4de7b9572d5a8dcf31c34ed6b2010bd80264d9dcb446b"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a8aa5d92b246dcb1da947aa1967d7eba64", "guid": "4b154dc3bd1054789ca4de7b9572d5a86314c508711ee43f1f82941376c6061b"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a80139e3bafc892345f556e2dd40259f0a", "guid": "4b154dc3bd1054789ca4de7b9572d5a85984af963c34c5b720e925d427c38766"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a8361d0818662248e996a839d4879e8c96", "guid": "4b154dc3bd1054789ca4de7b9572d5a846506ad6e8c49deba26dd52a82296908"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a8dffbbae62e2ebc76899a9d21f873773c", "guid": "4b154dc3bd1054789ca4de7b9572d5a8956d731b68f435c5f356d04260b942ce"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a886346c8f280abdbdf1b4b84aa0c3cf38", "guid": "4b154dc3bd1054789ca4de7b9572d5a80d94bb7c5952999af6a12c8ec4891fd8"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a89e0adbea9641f67f0e4545727592defd", "guid": "4b154dc3bd1054789ca4de7b9572d5a816f3f999a515836c823d37cf712d2e17"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a853ea3f4799076648ca2685c6da8d5522", "guid": "4b154dc3bd1054789ca4de7b9572d5a8a127d9e05fce62090e8a4813fddb8d06"}, {"fileReference": "4b154dc3bd1054789ca4de7b9572d5a811b6b696cdf26d6c0724ac703b685110", "guid": "4b154dc3bd1054789ca4de7b9572d5a8ea92425f66894094a16d0bf13dfb2955"}], "guid": "4b154dc3bd1054789ca4de7b9572d5a89dd38a620c8b372afb77c943524043b2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "4b154dc3bd1054789ca4de7b9572d5a8b37db0175b2d6af57ffff5957b69c3b9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "4b154dc3bd1054789ca4de7b9572d5a88de7368f220cc8540e840f528c6a3537", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "4b154dc3bd1054789ca4de7b9572d5a83ce2fd75e871004710bfb6ed775f4561", "name": "InvestmentTracker", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "4b154dc3bd1054789ca4de7b9572d5a84c600649b9e9f27255ca4c33a81c7cf7", "name": "InvestmentTracker.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}