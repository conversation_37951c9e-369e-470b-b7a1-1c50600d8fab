{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.0", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "4b154dc3bd1054789ca4de7b9572d5a883afe0ccdb3cb6e0e4ca113079c39035", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.0", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "4b154dc3bd1054789ca4de7b9572d5a8877b935958a4ad496a0ea0431935b84d", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a878c64aaf120eb90a7d3eb70642a039a1", "path": "InvestmentTrackerApp.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a87904764540c8ce8c1ac842ff6a869bfc", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a8274e999f88cd3799f199b7a002a96004", "path": "PortfolioView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a82b20199f89438b8e42d6552fc9ef9ab4", "path": "StocksView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a825f72d22f2a6430d3aa76d182913d9be", "path": "MutualFundsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a8d3339fb35e090a10c33f89d43e59c668", "path": "CryptoView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a8a647ad2d678df12604390b4a30f0093c", "path": "OtherInvestmentsView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "4b154dc3bd1054789ca4de7b9572d5a80612fc27cdeca31639e661126726e2e6", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a823bc32356bc9cefa731e8a90efc16dc3", "path": "Investment.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a8aa5d92b246dcb1da947aa1967d7eba64", "path": "SampleData.swift", "sourceTree": "<group>", "type": "file"}], "guid": "4b154dc3bd1054789ca4de7b9572d5a8b6be87b6db05ed55ed70e660cee8952a", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a80139e3bafc892345f556e2dd40259f0a", "path": "PortfolioViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "4b154dc3bd1054789ca4de7b9572d5a8b9d35cfda7c61cf4316348756d729756", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a8361d0818662248e996a839d4879e8c96", "path": "DataManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "4b154dc3bd1054789ca4de7b9572d5a873285e99cfb97b1a21445f5107c56515", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a8dffbbae62e2ebc76899a9d21f873773c", "path": "CurrencyFormatter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a886346c8f280abdbdf1b4b84aa0c3cf38", "path": "DateHelper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a89e0adbea9641f67f0e4545727592defd", "path": "PercentageCalculator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a853ea3f4799076648ca2685c6da8d5522", "path": "ProfitLossCalculator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "4b154dc3bd1054789ca4de7b9572d5a811b6b696cdf26d6c0724ac703b685110", "path": "XIRRCalculator.swift", "sourceTree": "<group>", "type": "file"}], "guid": "4b154dc3bd1054789ca4de7b9572d5a80f1fac37123519836e2a6058809bcf41", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "4b154dc3bd1054789ca4de7b9572d5a89dcbb48c7a7986f176d16e5103dfa40c", "name": "InvestmentTracker", "path": "InvestmentTracker", "sourceTree": "<group>", "type": "group"}, {"guid": "4b154dc3bd1054789ca4de7b9572d5a8031725b7e90937843dc0c54f52d201d1", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "4b154dc3bd1054789ca4de7b9572d5a872b055f778081adce001a59fbe0e5b7d", "name": "InvestmentTracker", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "4b154dc3bd1054789ca4de7b9572d5a8", "path": "/Users/<USER>/gitdir/investment-app/InvestmentTracker.xcodeproj", "projectDirectory": "/Users/<USER>/gitdir/investment-app", "targets": ["TARGET@v11_hash=b32559888b50ac60e3d72d1f0b4b8ca1"]}