{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/gitdir/investment-app/build/Build/Products": {"is-mutated": true}, "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app": {"is-mutated": true}, "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker": {"is-mutated": true}, "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib": {"is-mutated": true}, "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/_CodeSignature", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib", "/Users/<USER>/gitdir/investment-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "<Linked Binary Debug Dylib /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib>", "<target-InvestmentTracker-****************************************************************--begin-scanning>", "<target-InvestmentTracker-****************************************************************--end>", "<target-InvestmentTracker-****************************************************************--linker-inputs-ready>", "<target-InvestmentTracker-****************************************************************--modules-ready>", "<workspace-Debug-iphonesimulator18.5-iphonesimulator--stale-file-removal>"], "outputs": ["<all>"]}, "<target-InvestmentTracker-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/ssu/root.ssu.yaml", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/_CodeSignature", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Info.plist", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/PkgInfo", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent.der", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker Swift Compilation Finished", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_lto.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_dependency_info.dat", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker Swift Compilation Requirements Finished", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftmodule", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftsourceinfo", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.abi.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-Swift.h", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftdoc", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/InvestmentTracker-Swift.h", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-non-framework-target-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-target-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-generated-files.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-own-target-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-project-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyMetadataFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyStaticMetadataFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-OutputFileMap.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.LinkFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftConstValuesFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_const_extract_protocols.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/empty-InvestmentTracker.plist"], "roots": ["/tmp/InvestmentTracker.dst", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex", "/Users/<USER>/gitdir/investment-app/build/Build/Products"], "outputs": ["<target-InvestmentTracker-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>"]}, "<workspace-Debug-iphonesimulator18.5-iphonesimulator--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker-4b154dc3bd1054789ca4de7b9572d5a8-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Debug-iphonesimulator18.5-iphonesimulator--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /Users/<USER>/gitdir/investment-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /Users/<USER>/gitdir/investment-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/gitdir/investment-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "<ClangStatCache /Users/<USER>/gitdir/investment-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-o", "/Users/<USER>/gitdir/investment-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/gitdir/investment-app/InvestmentTracker.xcodeproj", "signature": "e73515fe74cf62c9e96806574a542c0a"}, "P0:::CreateBuildDirectory /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/gitdir/investment-app/build/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/gitdir/investment-app/build/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Products>", "/Users/<USER>/gitdir/investment-app/build/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator>", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker-4b154dc3bd1054789ca4de7b9572d5a8-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Metadata.appintents>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyMetadataFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyStaticMetadataFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftConstValuesFileList"], "outputs": ["<target-InvestmentTracker-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--Barrier-ChangePermissions>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--Barrier-StripSymbols>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>", "<CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib>", "<CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib>"], "outputs": ["<target-InvestmentTracker-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--Barrier-GenerateStubAPI>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ProductPostprocessingTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--Barrier-CodeSign>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>"], "outputs": ["<target-InvestmentTracker-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--Barrier-Validate>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>"], "outputs": ["<target-InvestmentTracker-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--Barrier-CopyAside>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>"], "outputs": ["<target-InvestmentTracker-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--GeneratedFilesTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ProductStructureTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent.der", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-InvestmentTracker-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-non-framework-target-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-target-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-generated-files.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-own-target-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-project-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.hmap"], "outputs": ["<target-InvestmentTracker-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Info.plist", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/PkgInfo", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/empty-InvestmentTracker.plist"], "outputs": ["<target-InvestmentTracker-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--RealityAssetsTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-InvestmentTracker-****************************************************************--ModuleMapTaskProducer>", "<target-InvestmentTracker-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-InvestmentTracker-****************************************************************--InfoPlistTaskProducer>", "<target-InvestmentTracker-****************************************************************--SanitizerTaskProducer>", "<target-InvestmentTracker-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-InvestmentTracker-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-InvestmentTracker-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-InvestmentTracker-****************************************************************--TestTargetTaskProducer>", "<target-InvestmentTracker-****************************************************************--TestHostTaskProducer>", "<target-InvestmentTracker-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-InvestmentTracker-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-InvestmentTracker-****************************************************************--DocumentationTaskProducer>", "<target-InvestmentTracker-****************************************************************--CustomTaskProducer>", "<target-InvestmentTracker-****************************************************************--StubBinaryTaskProducer>", "<target-InvestmentTracker-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--start>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>"], "outputs": ["<target-InvestmentTracker-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--HeadermapTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>"], "outputs": ["<target-InvestmentTracker-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ProductPostprocessingTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker Swift Compilation Finished", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_lto.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker Swift Compilation Requirements Finished", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftmodule", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftsourceinfo", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.abi.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-Swift.h", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftdoc", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-OutputFileMap.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.LinkFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_const_extract_protocols.json"], "outputs": ["<target-InvestmentTracker-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--generated-headers>"]}, "P0:::Gate target-InvestmentTracker-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--begin-compiling>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker Swift Compilation Requirements Finished", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftmodule", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftsourceinfo", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.abi.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-Swift.h", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftdoc", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/InvestmentTracker-Swift.h"], "outputs": ["<target-InvestmentTracker-****************************************************************--swift-generated-headers>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Metadata.appintents>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyMetadataFileList", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-InvestmentTracker-****************************************************************--entry>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Info.plist", "--temp-dir-path", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/ssu", "--bundle-id", "com.personal.InvestmentTracker", "--product-path", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "--extracted-metadata-path", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Metadata.appintents", "--metadata-file-list", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyMetadataFileList", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/gitdir/investment-app", "signature": "316d4ab4b5a2ea32586ead9e28183a89"}, "P0:target-InvestmentTracker-****************************************************************-:Debug:CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "inputs": ["/Users/<USER>/gitdir/investment-app/InvestmentTracker/ContentView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/InvestmentTrackerApp.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/Investment.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/SampleData.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Services/DataManager.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/CurrencyFormatter.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/DateHelper.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/PercentageCalculator.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/ProfitLossCalculator.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/XIRRCalculator.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/ViewModels/PortfolioViewModel.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/CryptoView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/MutualFundsView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/OtherInvestmentsView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/PortfolioView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/StocksView.swift/", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Info.plist/", "<CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib>", "<CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib>", "<target-InvestmentTracker-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker normal>", "<TRIGGER: MkDir /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/_CodeSignature", "<CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>", "<TRIGGER: CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib", "inputs": ["/Users/<USER>/gitdir/investment-app/InvestmentTracker/ContentView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/InvestmentTrackerApp.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/Investment.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/SampleData.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Services/DataManager.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/CurrencyFormatter.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/DateHelper.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/PercentageCalculator.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/ProfitLossCalculator.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/XIRRCalculator.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/ViewModels/PortfolioViewModel.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/CryptoView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/MutualFundsView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/OtherInvestmentsView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/PortfolioView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/StocksView.swift/", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Info.plist/", "<target-InvestmentTracker-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib", "inputs": ["/Users/<USER>/gitdir/investment-app/InvestmentTracker/ContentView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/InvestmentTrackerApp.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/Investment.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/SampleData.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Services/DataManager.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/CurrencyFormatter.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/DateHelper.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/PercentageCalculator.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/ProfitLossCalculator.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/XIRRCalculator.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/ViewModels/PortfolioViewModel.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/CryptoView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/MutualFundsView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/OtherInvestmentsView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/PortfolioView.swift/", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/StocksView.swift/", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Info.plist/", "<CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib>", "<target-InvestmentTracker-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>"], "deps": "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-InvestmentTracker-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/gitdir/investment-app/InvestmentTracker/InvestmentTrackerApp.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/ContentView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/PortfolioView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/StocksView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/MutualFundsView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/CryptoView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/OtherInvestmentsView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/Investment.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/SampleData.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/ViewModels/PortfolioViewModel.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Services/DataManager.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/CurrencyFormatter.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/DateHelper.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/PercentageCalculator.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/ProfitLossCalculator.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/XIRRCalculator.swift", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyMetadataFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyStaticMetadataFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_dependency_info.dat", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftConstValuesFileList", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-InvestmentTracker-****************************************************************--entry>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "InvestmentTracker", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "--xcode-version", "16F6", "--platform-family", "iOS", "--deployment-target", "18.0", "--bundle-identifier", "com.personal.InvestmentTracker", "--output", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "--target-triple", "arm64-apple-ios18.0-simulator", "--binary-file", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker", "--dependency-file", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftFileList", "--metadata-file-list", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/gitdir/investment-app", "signature": "1bc24b862ddaee3bd64629917fb43dfe"}, "P0:target-InvestmentTracker-****************************************************************-:Debug:Gate target-InvestmentTracker-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/InvestmentTracker.dst>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-InvestmentTracker-****************************************************************--begin-compiling>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:Gate target-InvestmentTracker-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/InvestmentTracker.dst>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-InvestmentTracker-****************************************************************--begin-linking>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:Gate target-InvestmentTracker-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/InvestmentTracker.dst>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--begin-scanning>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:Gate target-InvestmentTracker-****************************************************************--end": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--entry>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>", "<CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib>", "<CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib>", "<CopySwiftStdlib /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>", "<ExtractAppIntentsMetadata /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Metadata.appintents>", "<MkDir /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Info.plist", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/PkgInfo", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker Swift Compilation Finished", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.swiftconstvalues", "<Touch /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>", "<Validate /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_lto.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker Swift Compilation Requirements Finished", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftmodule", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftsourceinfo", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.abi.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-Swift.h", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftdoc", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/InvestmentTracker-Swift.h", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/InvestmentTracker-Swift.h", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-non-framework-target-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-target-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-generated-files.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-own-target-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-project-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyMetadataFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyStaticMetadataFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-OutputFileMap.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.LinkFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftConstValuesFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_const_extract_protocols.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/empty-InvestmentTracker.plist", "<target-InvestmentTracker-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-InvestmentTracker-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-InvestmentTracker-****************************************************************--Barrier-ChangePermissions>", "<target-InvestmentTracker-****************************************************************--Barrier-CodeSign>", "<target-InvestmentTracker-****************************************************************--Barrier-CopyAside>", "<target-InvestmentTracker-****************************************************************--Barrier-GenerateStubAPI>", "<target-InvestmentTracker-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-InvestmentTracker-****************************************************************--Barrier-RegisterProduct>", "<target-InvestmentTracker-****************************************************************--Barrier-StripSymbols>", "<target-InvestmentTracker-****************************************************************--Barrier-Validate>", "<target-InvestmentTracker-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-InvestmentTracker-****************************************************************--CustomTaskProducer>", "<target-InvestmentTracker-****************************************************************--DocumentationTaskProducer>", "<target-InvestmentTracker-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-InvestmentTracker-****************************************************************--GeneratedFilesTaskProducer>", "<target-InvestmentTracker-****************************************************************--HeadermapTaskProducer>", "<target-InvestmentTracker-****************************************************************--InfoPlistTaskProducer>", "<target-InvestmentTracker-****************************************************************--ModuleMapTaskProducer>", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--ProductPostprocessingTaskProducer>", "<target-InvestmentTracker-****************************************************************--ProductStructureTaskProducer>", "<target-InvestmentTracker-****************************************************************--RealityAssetsTaskProducer>", "<target-InvestmentTracker-****************************************************************--SanitizerTaskProducer>", "<target-InvestmentTracker-****************************************************************--StubBinaryTaskProducer>", "<target-InvestmentTracker-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-InvestmentTracker-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-InvestmentTracker-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-InvestmentTracker-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-InvestmentTracker-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-InvestmentTracker-****************************************************************--TestHostTaskProducer>", "<target-InvestmentTracker-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-InvestmentTracker-****************************************************************--TestTargetTaskProducer>", "<target-InvestmentTracker-****************************************************************--copy-headers-completion>", "<target-InvestmentTracker-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-InvestmentTracker-****************************************************************--generated-headers>", "<target-InvestmentTracker-****************************************************************--swift-generated-headers>"], "outputs": ["<target-InvestmentTracker-****************************************************************--end>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:Gate target-InvestmentTracker-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/InvestmentTracker.dst>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-InvestmentTracker-****************************************************************--begin-compiling>"], "outputs": ["<target-InvestmentTracker-****************************************************************--entry>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:Gate target-InvestmentTracker-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/InvestmentTracker.dst>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-InvestmentTracker-****************************************************************--immediate>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:Gate target-InvestmentTracker-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--begin-compiling>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-ExecutorLinkFileList-normal-arm64.txt", "<Linked Binary /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_lto.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker Swift Compilation Requirements Finished", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftmodule", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftsourceinfo", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.abi.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-Swift.h", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftdoc", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.LinkFileList"], "outputs": ["<target-InvestmentTracker-****************************************************************--linker-inputs-ready>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:Gate target-InvestmentTracker-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--begin-compiling>", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker Swift Compilation Requirements Finished", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftmodule", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftsourceinfo", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.abi.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-Swift.h", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftdoc", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/InvestmentTracker-Swift.h"], "outputs": ["<target-InvestmentTracker-****************************************************************--modules-ready>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:Gate target-InvestmentTracker-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--begin-compiling>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/ssu/root.ssu.yaml", "<CopySwiftStdlib /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>", "<ExtractAppIntentsMetadata /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Metadata.appintents>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent.der", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker Swift Compilation Finished", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_lto.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker Swift Compilation Requirements Finished", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftmodule", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftsourceinfo", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.abi.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-Swift.h", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftdoc", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/InvestmentTracker-Swift.h", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyMetadataFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyStaticMetadataFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-OutputFileMap.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.LinkFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftConstValuesFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_const_extract_protocols.json", "<target-InvestmentTracker-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-InvestmentTracker-****************************************************************--unsigned-product-ready>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:Gate target-InvestmentTracker-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-InvestmentTracker-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-InvestmentTracker-****************************************************************--will-sign>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:MkDir /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "inputs": ["<target-InvestmentTracker-****************************************************************--start>", "<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "<MkDir /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>", "<TRIGGER: MkDir /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Info.plist /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/empty-InvestmentTracker.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Info.plist /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/empty-InvestmentTracker.plist", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/empty-InvestmentTracker.plist", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--entry>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Info.plist", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/PkgInfo"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:ProcessProductPackaging  /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/Entitlements-Simulated.plist", "<target-InvestmentTracker-****************************************************************--ProductStructureTaskProducer>", "<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent", "<target-InvestmentTracker-****************************************************************--ProductStructureTaskProducer>", "<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent", "-o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/gitdir/investment-app", "signature": "a719c26692a01375a737f0bd5a1f9b95"}, "P0:target-InvestmentTracker-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "<target-InvestmentTracker-****************************************************************--Barrier-CodeSign>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:SwiftDriver Compilation InvestmentTracker normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation InvestmentTracker normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/gitdir/investment-app/InvestmentTracker/InvestmentTrackerApp.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/ContentView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/PortfolioView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/StocksView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/MutualFundsView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/CryptoView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/OtherInvestmentsView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/Investment.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/SampleData.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/ViewModels/PortfolioViewModel.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Services/DataManager.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/CurrencyFormatter.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/DateHelper.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/PercentageCalculator.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/ProfitLossCalculator.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/XIRRCalculator.swift", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-OutputFileMap.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_const_extract_protocols.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-generated-files.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-own-target-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-target-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-project-headers.hmap", "<ClangStatCache /Users/<USER>/gitdir/investment-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-InvestmentTracker-****************************************************************--generated-headers>", "<target-InvestmentTracker-****************************************************************--copy-headers-completion>", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker Swift Compilation Finished", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.stringsdata", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.swiftconstvalues", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.swiftconstvalues"]}, "P0:target-InvestmentTracker-****************************************************************-:Debug:Touch /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app": {"tool": "shell", "description": "Touch /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "<target-InvestmentTracker-****************************************************************--Barrier-Validate>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app"], "env": {}, "working-directory": "/Users/<USER>/gitdir/investment-app", "signature": "5d8b1f879c1f2765c3f0718c11aa43fd"}, "P0:target-InvestmentTracker-****************************************************************-:Debug:Validate /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/Info.plist", "<target-InvestmentTracker-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-InvestmentTracker-****************************************************************--will-sign>", "<target-InvestmentTracker-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>"], "outputs": ["<Validate /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app>"]}, "P2:::WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker-4b154dc3bd1054789ca4de7b9572d5a8-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker-4b154dc3bd1054789ca4de7b9572d5a8-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker-4b154dc3bd1054789ca4de7b9572d5a8-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:ConstructStubExecutorLinkFileList /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-ExecutorLinkFileList-normal-arm64.txt": {"tool": "construct-stub-executor-input-file-list", "description": "ConstructStubExecutorLinkFileList /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-ExecutorLinkFileList-normal-arm64.txt", "inputs": ["/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-ExecutorLinkFileList-normal-arm64.txt"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:Copy /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftsourceinfo", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftsourceinfo/", "<target-InvestmentTracker-****************************************************************--copy-headers-completion>", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:Copy /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.abi.json", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.abi.json/", "<target-InvestmentTracker-****************************************************************--copy-headers-completion>", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.abi.json"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:Copy /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftdoc", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftdoc/", "<target-InvestmentTracker-****************************************************************--copy-headers-completion>", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftdoc"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:Copy /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftmodule", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftmodule/", "<target-InvestmentTracker-****************************************************************--copy-headers-completion>", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.swiftmodule/arm64-apple-ios-simulator.swiftmodule"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker normal": {"tool": "shell", "description": "Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker normal", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent.der", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker", "<Linked Binary /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker>", "<TRIGGER: Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.0-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-e", "___debug_blank_executor_main", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_dylib", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibPath-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_instlnm", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibInstallName-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-filelist", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-ExecutorLinkFileList-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent.der", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker"], "env": {}, "working-directory": "/Users/<USER>/gitdir/investment-app", "signature": "4c35ffe42535d424f2c00ba5ec887a7d"}, "P2:target-InvestmentTracker-****************************************************************-:Debug:Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib normal", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTrackerApp.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/StocksView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/MutualFundsView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CryptoView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/OtherInvestmentsView.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/Investment.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/SampleData.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PortfolioViewModel.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DataManager.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/CurrencyFormatter.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/DateHelper.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/PercentageCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/ProfitLossCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/XIRRCalculator.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.LinkFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent.der", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator", "<target-InvestmentTracker-****************************************************************--generated-headers>", "<target-InvestmentTracker-****************************************************************--swift-generated-headers>", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib", "<Linked Binary Debug Dylib /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_lto.o", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.0-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-L/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-F/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator", "-filelist", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.LinkFileList", "-install_name", "@rpath/InvestmentTracker.debug.dylib", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftmodule", "-<PERSON><PERSON><PERSON>", "-alias", "-<PERSON><PERSON><PERSON>", "_main", "-<PERSON><PERSON><PERSON>", "___debug_main_executable_dylib_entry_point", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/InvestmentTracker.debug.dylib"], "env": {}, "working-directory": "/Users/<USER>/gitdir/investment-app", "deps": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_dependency_info.dat"], "deps-style": "dependency-info", "signature": "406d1e7b95ca06135c3ee5da2b3cd6b8"}, "P2:target-InvestmentTracker-****************************************************************-:Debug:Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib normal", "inputs": ["<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib", "<Linked Binary Preview Injection Dylib /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib>", "<TRIGGER: Ld /Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.0-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator", "-install_name", "@rpath/InvestmentTracker.debug.dylib", "-dead_strip", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_dependency_info.dat", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.app-Simulated.xcent.der", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/gitdir/investment-app/build/Build/Products/Debug-iphonesimulator/InvestmentTracker.app/__preview.dylib"], "env": {}, "working-directory": "/Users/<USER>/gitdir/investment-app", "signature": "c72fb5e6e3e32d9398361b4002c75baa"}, "P2:target-InvestmentTracker-****************************************************************-:Debug:SwiftDriver Compilation Requirements InvestmentTracker normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements InvestmentTracker normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/gitdir/investment-app/InvestmentTracker/InvestmentTrackerApp.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/ContentView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/PortfolioView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/StocksView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/MutualFundsView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/CryptoView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Views/OtherInvestmentsView.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/Investment.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Models/SampleData.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/ViewModels/PortfolioViewModel.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Services/DataManager.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/CurrencyFormatter.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/DateHelper.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/PercentageCalculator.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/ProfitLossCalculator.swift", "/Users/<USER>/gitdir/investment-app/InvestmentTracker/Utils/XIRRCalculator.swift", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftFileList", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-OutputFileMap.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_const_extract_protocols.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-generated-files.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-own-target-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-target-headers.hmap", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-project-headers.hmap", "<ClangStatCache /Users/<USER>/gitdir/investment-app/build/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-InvestmentTracker-****************************************************************--copy-headers-completion>", "<target-InvestmentTracker-****************************************************************--ModuleVerifierTaskProducer>", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker Swift Compilation Requirements Finished", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftmodule", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftsourceinfo", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.abi.json", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-Swift.h", "/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.swiftdoc"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/InvestmentTracker-Swift.h /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/InvestmentTracker-Swift.h /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-Swift.h", "inputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-Swift.h", "<target-InvestmentTracker-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/InvestmentTracker-Swift.h"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibInstallName-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibInstallName-normal-arm64.txt", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibInstallName-normal-arm64.txt"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibPath-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibPath-normal-arm64.txt", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-DebugDylibPath-normal-arm64.txt"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-non-framework-target-headers.hmap", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-non-framework-target-headers.hmap"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-target-headers.hmap", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-all-target-headers.hmap"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-generated-files.hmap", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-generated-files.hmap"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-own-target-headers.hmap", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-own-target-headers.hmap"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-project-headers.hmap", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker-project-headers.hmap"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyMetadataFileList", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyMetadataFileList"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyStaticMetadataFileList", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.DependencyStaticMetadataFileList"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.hmap", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/InvestmentTracker.hmap"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-OutputFileMap.json", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker-OutputFileMap.json"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.LinkFileList", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.LinkFileList"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftConstValuesFileList", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftConstValuesFileList"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftFileList", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker.SwiftFileList"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_const_extract_protocols.json", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/Objects-normal/arm64/InvestmentTracker_const_extract_protocols.json"]}, "P2:target-InvestmentTracker-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/empty-InvestmentTracker.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/empty-InvestmentTracker.plist", "inputs": ["<target-InvestmentTracker-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/gitdir/investment-app/build/Build/Intermediates.noindex/InvestmentTracker.build/Debug-iphonesimulator/InvestmentTracker.build/empty-InvestmentTracker.plist"]}}}