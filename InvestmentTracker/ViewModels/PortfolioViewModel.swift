import Foundation

class PortfolioViewModel: ObservableObject {
    @Published var stocks: [Stock] = []
    @Published var mutualFunds: [MutualFund] = []
    @Published var cryptos: [Crypto] = []
    @Published var fixedDeposits: [FixedDeposit] = []
    @Published var epf: [EPF] = []
    @Published var nps: [NPS] = []
    @Published var rsus: [RSU] = []
    @Published var goldInvestments: [Gold] = []
    
    @Published var isLoading = false
    
    // MARK: - Computed Properties
    var totalPortfolioValue: Double {
        return stocksValue + mutualFundsValue + cryptoValue + fixedDepositsValue + 
               epfValue + npsValue + rsuValue + goldValue
    }
    
    var totalInvestedAmount: Double {
        return stocksInvested + mutualFundsInvested + cryptoInvested + fixedDepositsInvested + 
               epfInvested + npsInvested + rsuInvested + goldInvested
    }
    
    var totalProfitLoss: Double {
        return totalPortfolioValue - totalInvestedAmount
    }
    
    var totalProfitLossPercentage: Double {
        guard totalInvestedAmount > 0 else { return 0 }
        return (totalProfitLoss / totalInvestedAmount) * 100
    }
    
    // MARK: - Individual Asset Class Values
    var stocksValue: Double {
        stocks.reduce(0) { $0 + $1.currentValue }
    }
    
    var stocksInvested: Double {
        stocks.reduce(0) { $0 + $1.investedAmount }
    }
    
    var mutualFundsValue: Double {
        mutualFunds.reduce(0) { $0 + $1.currentValue }
    }
    
    var mutualFundsInvested: Double {
        mutualFunds.reduce(0) { $0 + $1.investedAmount }
    }
    
    var cryptoValue: Double {
        cryptos.reduce(0) { $0 + $1.currentValue }
    }
    
    var cryptoInvested: Double {
        cryptos.reduce(0) { $0 + $1.investedAmount }
    }
    
    var fixedDepositsValue: Double {
        fixedDeposits.reduce(0) { $0 + $1.currentValue }
    }
    
    var fixedDepositsInvested: Double {
        fixedDeposits.reduce(0) { $0 + $1.investedAmount }
    }
    
    var epfValue: Double {
        epf.reduce(0) { $0 + $1.currentValue }
    }
    
    var epfInvested: Double {
        epf.reduce(0) { $0 + $1.investedAmount }
    }
    
    var npsValue: Double {
        nps.reduce(0) { $0 + $1.currentValue }
    }
    
    var npsInvested: Double {
        nps.reduce(0) { $0 + $1.investedAmount }
    }
    
    var rsuValue: Double {
        rsus.reduce(0) { $0 + $1.currentValue }
    }
    
    var rsuInvested: Double {
        rsus.reduce(0) { $0 + $1.investedAmount }
    }
    
    var goldValue: Double {
        goldInvestments.reduce(0) { $0 + $1.currentValue }
    }
    
    var goldInvested: Double {
        goldInvestments.reduce(0) { $0 + $1.investedAmount }
    }
    
    // MARK: - Data Management Methods
    @MainActor
    func refreshData() {
        isLoading = true

        Task {
            // Refresh crypto prices for existing holdings
            await refreshCryptoPrices()

            // Sync CoinDCX portfolio if credentials are available
            await syncCoinDCXPortfolio()

            isLoading = false
        }
    }
    
    @MainActor
    func saveAllData() {
        let dataManager = DataManager.shared
        dataManager.saveStocks(stocks)
        dataManager.saveMutualFunds(mutualFunds)
        dataManager.saveCryptos(cryptos)
        dataManager.saveFixedDeposits(fixedDeposits)
        dataManager.saveEPF(epf)
        dataManager.saveNPS(nps)
        dataManager.saveRSUs(rsus)
        dataManager.saveGold(goldInvestments)
    }
    
    @MainActor
    func loadAllData() {
        let dataManager = DataManager.shared
        stocks = dataManager.loadStocks()
        mutualFunds = dataManager.loadMutualFunds()
        cryptos = dataManager.loadCryptos()
        fixedDeposits = dataManager.loadFixedDeposits()
        epf = dataManager.loadEPF()
        nps = dataManager.loadNPS()
        rsus = dataManager.loadRSUs()
        goldInvestments = dataManager.loadGold()
    }
    
    @MainActor
    func clearAllData() {
        stocks = []
        mutualFunds = []
        cryptos = []
        fixedDeposits = []
        epf = []
        nps = []
        rsus = []
        goldInvestments = []
        
        // Clear from storage as well
        let dataManager = DataManager.shared
        dataManager.saveStocks([])
        dataManager.saveMutualFunds([])
        dataManager.saveCryptos([])
        dataManager.saveFixedDeposits([])
        dataManager.saveEPF([])
        dataManager.saveNPS([])
        dataManager.saveRSUs([])
        dataManager.saveGold([])
    }
    
    @MainActor
    func loadSampleData() {
        let factory = SampleDataFactory.shared
        
        stocks = factory.sampleStocks()
        mutualFunds = factory.sampleMutualFunds()
        cryptos = factory.sampleCryptos()
        fixedDeposits = factory.sampleFixedDeposits()
        epf = factory.sampleEPF()
        nps = factory.sampleNPS()
        rsus = factory.sampleRSUs()
        goldInvestments = factory.sampleGold()
        
        // Save the sample data
        saveAllData()
    }
    
    // MARK: - Fixed Deposits CRUD Operations
    @MainActor
    func addFixedDeposit(_ fd: FixedDeposit) {
        fixedDeposits.append(fd)
        DataManager.shared.saveFixedDeposits(fixedDeposits)
    }
    
    @MainActor
    func updateFixedDeposit(_ originalFD: FixedDeposit, with newFD: FixedDeposit) {
        if let index = fixedDeposits.firstIndex(where: { $0.id == originalFD.id }) {
            // Create updated FD with new values
            fixedDeposits[index] = FixedDeposit(
                bankName: newFD.name,
                investedAmount: newFD.investedAmount,
                interestRate: newFD.interestRate,
                investmentDate: newFD.investmentDate,
                maturityDate: newFD.maturityDate,
                compoundingFrequency: newFD.compoundingFrequency
            )
            DataManager.shared.saveFixedDeposits(fixedDeposits)
        }
    }
    
    @MainActor
    func deleteFixedDeposit(_ fd: FixedDeposit) {
        fixedDeposits.removeAll { $0.id == fd.id }
        DataManager.shared.saveFixedDeposits(fixedDeposits)
    }
    
    // MARK: - EPF CRUD Operations
    @MainActor
    func addEPF(_ epf: EPF) {
        self.epf.append(epf)
        DataManager.shared.saveEPF(self.epf)
    }
    
    @MainActor
    func updateEPF(_ originalEPF: EPF, with newEPF: EPF) {
        if let index = epf.firstIndex(where: { $0.id == originalEPF.id }) {
            epf[index] = EPF(
                investedAmount: newEPF.investedAmount,
                currentValue: newEPF.currentValue,
                investmentDate: newEPF.investmentDate
            )
            DataManager.shared.saveEPF(epf)
        }
    }
    
    @MainActor
    func deleteEPF(_ epf: EPF) {
        self.epf.removeAll { $0.id == epf.id }
        DataManager.shared.saveEPF(self.epf)
    }
    
    // MARK: - NPS CRUD Operations
    @MainActor
    func addNPS(_ nps: NPS) {
        self.nps.append(nps)
        DataManager.shared.saveNPS(self.nps)
    }
    
    @MainActor
    func updateNPS(_ originalNPS: NPS, with newNPS: NPS) {
        if let index = nps.firstIndex(where: { $0.id == originalNPS.id }) {
            nps[index] = NPS(
                investedAmount: newNPS.investedAmount,
                currentValue: newNPS.currentValue,
                investmentDate: newNPS.investmentDate
            )
            DataManager.shared.saveNPS(nps)
        }
    }
    
    @MainActor
    func deleteNPS(_ nps: NPS) {
        self.nps.removeAll { $0.id == nps.id }
        DataManager.shared.saveNPS(self.nps)
    }
    
    // MARK: - RSU CRUD Operations
    @MainActor
    func addRSU(_ rsu: RSU) {
        self.rsus.append(rsu)
        DataManager.shared.saveRSUs(self.rsus)
    }
    
    @MainActor
    func updateRSU(_ originalRSU: RSU, with newRSU: RSU) {
        if let index = rsus.firstIndex(where: { $0.id == originalRSU.id }) {
            rsus[index] = RSU(
                companyName: newRSU.name,
                numberOfStocks: newRSU.numberOfStocks,
                unitPriceUSD: newRSU.unitPriceUSD,
                currentPriceUSD: newRSU.currentPriceUSD,
                exchangeRate: newRSU.exchangeRate,
                investmentDate: newRSU.investmentDate
            )
            DataManager.shared.saveRSUs(rsus)
        }
    }
    
    @MainActor
    func deleteRSU(_ rsu: RSU) {
        self.rsus.removeAll { $0.id == rsu.id }
        DataManager.shared.saveRSUs(self.rsus)
    }
    
    // MARK: - Crypto CRUD Operations
    @MainActor
    func addCrypto(_ crypto: Crypto) {
        self.cryptos.append(crypto)
        DataManager.shared.saveCryptos(self.cryptos)
    }
    
    @MainActor
    func updateCrypto(_ id: UUID, with newCrypto: Crypto) {
        if let index = cryptos.firstIndex(where: { $0.id == id }) {
            cryptos[index] = newCrypto  // Use the updated crypto directly to preserve lastUpdated
            DataManager.shared.saveCryptos(cryptos)
        }
    }
    
    @MainActor
    func deleteCrypto(_ id: UUID) {
        cryptos.removeAll { $0.id == id }
        DataManager.shared.saveCryptos(cryptos)
    }

    // MARK: - Stock CRUD Operations
    @MainActor
    func addStock(_ stock: Stock) {
        self.stocks.append(stock)
        DataManager.shared.saveStocks(self.stocks)
    }
    
    @MainActor
    func updateStock(_ stock: Stock) {
        if let index = stocks.firstIndex(where: { $0.id == stock.id }) {
            stocks[index] = stock
            DataManager.shared.saveStocks(stocks)
        }
    }
    
    @MainActor
    func deleteStock(_ stock: Stock) {
        stocks.removeAll { $0.id == stock.id }
        DataManager.shared.saveStocks(stocks)
    }
    
    // MARK: - MutualFund CRUD Operations
    @MainActor
    func addMutualFund(name: String, schemeCode: String, units: Double, purchaseNAV: Double, currentNAV: Double, investmentDate: Date) {
        let mutualFund = MutualFund(
            name: name,
            schemeCode: schemeCode,
            units: units,
            purchaseNAV: purchaseNAV,
            currentNAV: currentNAV,
            investmentDate: investmentDate
        )
        self.mutualFunds.append(mutualFund)
        DataManager.shared.saveMutualFunds(self.mutualFunds)
    }
    
    @MainActor
    func updateMutualFund(id: UUID, name: String, schemeCode: String, units: Double, purchaseNAV: Double, currentNAV: Double, investmentDate: Date) {
        if let index = mutualFunds.firstIndex(where: { $0.id == id }) {
            mutualFunds[index] = MutualFund(
                name: name,
                schemeCode: schemeCode,
                units: units,
                purchaseNAV: purchaseNAV,
                currentNAV: currentNAV,
                investmentDate: investmentDate
            )
            DataManager.shared.saveMutualFunds(mutualFunds)
        }
    }
    
    @MainActor
    func updateMutualFundNAV(id: UUID, newNAV: Double) {
        if let index = mutualFunds.firstIndex(where: { $0.id == id }) {
            mutualFunds[index].updateCurrentNAV(newNAV)
            DataManager.shared.saveMutualFunds(mutualFunds)
        }
    }
    
    @MainActor
    func deleteMutualFund(id: UUID) {
        mutualFunds.removeAll { $0.id == id }
        DataManager.shared.saveMutualFunds(mutualFunds)
    }
    
    // MARK: - Stock Price Updates
    @MainActor
    func refreshStockPrices() {
        guard !stocks.isEmpty else { return }
        
        Task {
            let symbols = stocks.map { (symbol: $0.symbol, exchange: $0.exchange) }
            let prices = await StockPriceService.shared.fetchMultipleStockPrices(symbols: symbols)
            
            for index in stocks.indices {
                let stock = stocks[index]
                if let newPrice = prices[stock.symbol] {
                    stocks[index].updateCurrentPrice(newPrice)
                }
            }
            
            DataManager.shared.saveStocks(stocks)
        }
    }
    
    @MainActor
    func refreshSingleStockPrice(_ stock: Stock) {
        Task {
            if let newPrice = await StockPriceService.shared.fetchStockPrice(symbol: stock.symbol, exchange: stock.exchange) {
                if let index = stocks.firstIndex(where: { $0.id == stock.id }) {
                    stocks[index].updateCurrentPrice(newPrice)
                    DataManager.shared.saveStocks(stocks)
                }
            }
        }
    }
    
    // MARK: - Crypto Price Updates
    @MainActor
    func refreshCryptoPrices() async {
        guard !cryptos.isEmpty else { return }

        for index in cryptos.indices {
            let crypto = cryptos[index]
            if let newPrice = await CryptoPriceService.shared.fetchCurrentPrice(for: crypto.symbol) {
                cryptos[index] = crypto.updateCurrentPrice(newPrice)
            }
        }

        DataManager.shared.saveCryptos(cryptos)
    }
    
    @MainActor
    func refreshSingleCryptoPrice(_ crypto: Crypto) {
        Task {
            if let newPrice = await CryptoPriceService.shared.fetchCurrentPrice(for: crypto.symbol) {
                if let index = cryptos.firstIndex(where: { $0.id == crypto.id }) {
                    cryptos[index] = crypto.updateCurrentPrice(newPrice)
                    DataManager.shared.saveCryptos(cryptos)
                }
            }
        }
    }

    // MARK: - CoinDCX Portfolio Sync
    @MainActor
    func syncCoinDCXPortfolio() async {
        guard KeychainManager.shared.hasCoinDCXCredentials() else {
            print("No CoinDCX credentials found, skipping sync")
            return
        }

        let credentials = KeychainManager.shared.getCoinDCXCredentials()
        guard let apiKey = credentials.apiKey,
              let apiSecret = credentials.apiSecret else {
            print("Invalid CoinDCX credentials")
            return
        }

        do {
            let balances = try await CoinDCXService.shared.fetchPortfolioBalances(
                apiKey: apiKey,
                apiSecret: apiSecret
            )

            let apiCryptos = await CoinDCXService.shared.convertBalancesToCryptos(
                balances,
                apiKey: apiKey,
                apiSecret: apiSecret
            )
            await mergeCoinDCXCryptos(apiCryptos)

        } catch {
            print("Failed to sync CoinDCX portfolio: \(error.localizedDescription)")
        }
    }

    @MainActor
    private func mergeCoinDCXCryptos(_ apiCryptos: [Crypto]) async {
        for apiCrypto in apiCryptos {
            // Check if we already have this crypto from CoinDCX
            if let existingIndex = cryptos.firstIndex(where: {
                $0.symbol == apiCrypto.symbol && $0.exchange == "CoinDCX" && $0.isFromAPI
            }) {
                // Update existing API-sourced crypto
                cryptos[existingIndex] = apiCrypto
            } else {
                // Add new crypto from API
                cryptos.append(apiCrypto)
            }
        }

        // Remove API-sourced cryptos that are no longer in the API response (zero balance)
        cryptos.removeAll { crypto in
            crypto.isFromAPI &&
            crypto.exchange == "CoinDCX" &&
            !apiCryptos.contains { $0.symbol == crypto.symbol }
        }

        DataManager.shared.saveCryptos(cryptos)
    }

    @MainActor
    func forceSyncCoinDCXPortfolio() async -> Bool {
        guard KeychainManager.shared.hasCoinDCXCredentials() else {
            return false
        }

        let credentials = KeychainManager.shared.getCoinDCXCredentials()
        guard let apiKey = credentials.apiKey,
              let apiSecret = credentials.apiSecret else {
            return false
        }

        isLoading = true

        do {
            let balances = try await CoinDCXService.shared.fetchPortfolioBalances(
                apiKey: apiKey,
                apiSecret: apiSecret
            )

            let apiCryptos = await CoinDCXService.shared.convertBalancesToCryptos(
                balances,
                apiKey: apiKey,
                apiSecret: apiSecret
            )
            await mergeCoinDCXCryptos(apiCryptos)

            isLoading = false
            return true

        } catch {
            isLoading = false
            print("Failed to force sync CoinDCX portfolio: \(error.localizedDescription)")
            return false
        }
    }

    // MARK: - Exchange Rate Updates
    @MainActor
    func updateExchangeRates(newRate: Double) {
        // Update all RSUs with the new exchange rate by recreating them
        for index in rsus.indices {
            let originalRSU = rsus[index]
            rsus[index] = RSU(
                companyName: originalRSU.name,
                numberOfStocks: originalRSU.numberOfStocks,
                unitPriceUSD: originalRSU.unitPriceUSD,
                currentPriceUSD: originalRSU.currentPriceUSD,
                exchangeRate: newRate,
                investmentDate: originalRSU.investmentDate
            )
        }
        DataManager.shared.saveRSUs(rsus)
    }
    
    @MainActor
    func refreshPortfolioValues() {
        // Fetch live exchange rates from API
        Task {
            await ExchangeRateService.shared.fetchLatestExchangeRate()
            let currentExchangeRate = ExchangeRateService.shared.currentUSDToINRRate
            updateExchangeRates(newRate: currentExchangeRate)
        }
        
        // Refresh stock prices
        refreshStockPrices()
        
        // Refresh crypto prices
        Task {
            await refreshCryptoPrices()
        }
        
        // Refresh gold prices
        refreshGoldPrices()
    }

    // MARK: - Gold CRUD Operations
    @MainActor
    func addGold(_ gold: Gold) {
        self.goldInvestments.append(gold)
        DataManager.shared.saveGold(self.goldInvestments)
    }
    
    @MainActor
    func updateGold(_ originalGold: Gold, with newGold: Gold) {
        if let index = goldInvestments.firstIndex(where: { $0.id == originalGold.id }) {
            goldInvestments[index] = newGold
            DataManager.shared.saveGold(goldInvestments)
        }
    }
    
    @MainActor
    func deleteGold(_ gold: Gold) {
        self.goldInvestments.removeAll { $0.id == gold.id }
        DataManager.shared.saveGold(self.goldInvestments)
    }

    // MARK: - Gold Price Updates
    @MainActor
    func refreshGoldPrices() {
        guard !goldInvestments.isEmpty else { return }
        
        Task {
            if let prices = await GoldPriceService.shared.fetchCurrentGoldPrices() {
                for index in goldInvestments.indices {
                    let originalGold = goldInvestments[index]
                    let newPrice = originalGold.purity == .kt22 ? prices.kt22 : prices.kt24
                    
                    goldInvestments[index] = Gold(
                        name: originalGold.name,
                        grams: originalGold.grams,
                        purity: originalGold.purity,
                        purchaseRate: originalGold.purchaseRate,
                        currentRate: newPrice,
                        investmentDate: originalGold.investmentDate,
                        totalPrice: originalGold.totalPrice
                    )
                }
                DataManager.shared.saveGold(goldInvestments)
            }
        }
    }
    
    @MainActor
    func refreshSingleGoldPrice(_ goldItem: Gold) {
        Task {
            if let prices = await GoldPriceService.shared.fetchCurrentGoldPrices() {
                if let index = goldInvestments.firstIndex(where: { $0.id == goldItem.id }) {
                    let newPrice = goldItem.purity == .kt22 ? prices.kt22 : prices.kt24
                    
                    goldInvestments[index] = Gold(
                        name: goldItem.name,
                        grams: goldItem.grams,
                        purity: goldItem.purity,
                        purchaseRate: goldItem.purchaseRate,
                        currentRate: newPrice,
                        investmentDate: goldItem.investmentDate,
                        totalPrice: goldItem.totalPrice
                    )
                    DataManager.shared.saveGold(goldInvestments)
                }
            }
        }
    }

    // MARK: - Debug/Testing Methods
    #if DEBUG
    @MainActor
    func testCoinDCXIntegration() async {
        print("🧪 Testing CoinDCX Integration...")

        // Test 1: Check if keychain manager works
        let testKey = "test_key_123"
        let testSecret = "test_secret_456"

        let saveResult = KeychainManager.shared.saveCoinDCXCredentials(apiKey: testKey, apiSecret: testSecret)
        print("🧪 Keychain save test: \(saveResult ? "✅ PASS" : "❌ FAIL")")

        let retrievedCredentials = KeychainManager.shared.getCoinDCXCredentials()
        let retrieveResult = retrievedCredentials.apiKey == testKey && retrievedCredentials.apiSecret == testSecret
        print("🧪 Keychain retrieve test: \(retrieveResult ? "✅ PASS" : "❌ FAIL")")

        // Test 2: Test crypto model enhancements
        let testCrypto = Crypto(
            fromAPI: "Bitcoin",
            symbol: "BTC",
            exchange: "CoinDCX",
            availableBalance: 0.1,
            lockedBalance: 0.05,
            currentPrice: 3200000.0
        )

        let modelTest = testCrypto.isFromAPI && testCrypto.totalBalance == 0.15 && testCrypto.quantity == 0.15
        print("🧪 Enhanced crypto model test: \(modelTest ? "✅ PASS" : "❌ FAIL")")

        // Test 3: Test CoinDCX service
        let balanceTest = CoinDCXService.shared.validateCredentials(apiKey: testKey, apiSecret: testSecret)
        print("🧪 CoinDCX service validation test: \(balanceTest ? "✅ PASS" : "❌ FAIL")")

        // Clean up test data
        _ = KeychainManager.shared.deleteCoinDCXCredentials()

        print("🧪 CoinDCX Integration Test Complete!")
    }
    #endif
}