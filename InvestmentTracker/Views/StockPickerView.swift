import SwiftUI

struct StockPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    @ObservedObject private var stockPriceService = StockPriceService.shared
    
    @State private var searchText = ""
    @State private var selectedStocks: Set<PopularStock> = []
    
    var body: some View {
        NavigationView {
            VStack {
                SearchBar(text: $searchText)
                
                List {
                    ForEach(filteredStocks, id: \.symbol) { stock in
                        PopularStockRow(
                            stock: stock,
                            isSelected: selectedStocks.contains(stock),
                            onToggle: {
                                if selectedStocks.contains(stock) {
                                    selectedStocks.remove(stock)
                                } else {
                                    selectedStocks.insert(stock)
                                }
                            }
                        )
                    }
                }
                .listStyle(.plain)
            }
            .navigationTitle("Popular Stocks")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add Selected (\(selectedStocks.count))") {
                        addSelectedStocks()
                    }
                    .disabled(selectedStocks.isEmpty)
                }
            }
        }
    }
    
    private var filteredStocks: [PopularStock] {
        if searchText.isEmpty {
            return PopularStock.allStocks
        } else {
            return PopularStock.allStocks.filter { stock in
                stock.name.localizedCaseInsensitiveContains(searchText) ||
                stock.symbol.localizedCaseInsensitiveContains(searchText) ||
                stock.sector.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    private func addSelectedStocks() {
        Task {
            for popularStock in selectedStocks {
                // Fetch current price for each selected stock
                let currentPrice = await stockPriceService.fetchStockPrice(
                    symbol: popularStock.symbol,
                    exchange: popularStock.exchange
                ) ?? popularStock.approximatePrice
                
                let newStock = Stock(
                    name: popularStock.name,
                    symbol: popularStock.symbol,
                    exchange: popularStock.exchange,
                    quantity: 1, // Default quantity
                    purchasePrice: currentPrice,
                    currentPrice: currentPrice,
                    investmentDate: Date()
                )
                
                await MainActor.run {
                    portfolioViewModel.addStock(newStock)
                }
            }
            
            await MainActor.run {
                dismiss()
            }
        }
    }
}

struct SearchBar: View {
    @Binding var text: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("Search stocks...", text: $text)
                .textFieldStyle(.plain)
            
            if !text.isEmpty {
                Button(action: {
                    text = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(10)
        .padding(.horizontal)
    }
}

struct PopularStockRow: View {
    let stock: PopularStock
    let isSelected: Bool
    let onToggle: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(stock.symbol)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("(\(stock.exchange))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color(.systemGray5))
                        .cornerRadius(4)
                }
                
                Text(stock.name)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                
                Text(stock.sector)
                    .font(.caption)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(4)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("~₹\(String(format: "%.0f", stock.approximatePrice))")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Button(action: onToggle) {
                    Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                        .font(.title2)
                        .foregroundColor(isSelected ? .green : .secondary)
                }
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            onToggle()
        }
    }
}

// MARK: - Popular Stock Data Model
struct PopularStock: Hashable, Identifiable {
    let id = UUID()
    let symbol: String
    let name: String
    let exchange: String
    let sector: String
    let approximatePrice: Double
    
    static let allStocks: [PopularStock] = [
        // IT Sector
        PopularStock(symbol: "TCS", name: "Tata Consultancy Services", exchange: "NSE", sector: "IT", approximatePrice: 3800),
        PopularStock(symbol: "INFY", name: "Infosys Limited", exchange: "NSE", sector: "IT", approximatePrice: 1500),
        PopularStock(symbol: "HCLTECH", name: "HCL Technologies", exchange: "NSE", sector: "IT", approximatePrice: 1200),
        PopularStock(symbol: "WIPRO", name: "Wipro Limited", exchange: "NSE", sector: "IT", approximatePrice: 400),
        PopularStock(symbol: "TECHM", name: "Tech Mahindra", exchange: "NSE", sector: "IT", approximatePrice: 1100),
        
        // Banking & Finance
        PopularStock(symbol: "HDFCBANK", name: "HDFC Bank", exchange: "NSE", sector: "Banking", approximatePrice: 1600),
        PopularStock(symbol: "ICICIBANK", name: "ICICI Bank", exchange: "NSE", sector: "Banking", approximatePrice: 1200),
        PopularStock(symbol: "SBIN", name: "State Bank of India", exchange: "NSE", sector: "Banking", approximatePrice: 800),
        PopularStock(symbol: "AXISBANK", name: "Axis Bank", exchange: "NSE", sector: "Banking", approximatePrice: 1100),
        PopularStock(symbol: "KOTAKBANK", name: "Kotak Mahindra Bank", exchange: "NSE", sector: "Banking", approximatePrice: 1700),
        
        // FMCG
        PopularStock(symbol: "HINDUNILVR", name: "Hindustan Unilever", exchange: "NSE", sector: "FMCG", approximatePrice: 2400),
        PopularStock(symbol: "ITC", name: "ITC Limited", exchange: "NSE", sector: "FMCG", approximatePrice: 400),
        PopularStock(symbol: "NESTLEIND", name: "Nestle India", exchange: "NSE", sector: "FMCG", approximatePrice: 2200),
        PopularStock(symbol: "BRITANNIA", name: "Britannia Industries", exchange: "NSE", sector: "FMCG", approximatePrice: 4800),
        
        // Auto
        PopularStock(symbol: "MARUTI", name: "Maruti Suzuki India", exchange: "NSE", sector: "Auto", approximatePrice: 11000),
        PopularStock(symbol: "TATAMOTORS", name: "Tata Motors", exchange: "NSE", sector: "Auto", approximatePrice: 900),
        PopularStock(symbol: "M&M", name: "Mahindra & Mahindra", exchange: "NSE", sector: "Auto", approximatePrice: 2800),
        PopularStock(symbol: "BAJAJ-AUTO", name: "Bajaj Auto", exchange: "NSE", sector: "Auto", approximatePrice: 9000),
        
        // Pharma
        PopularStock(symbol: "SUNPHARMA", name: "Sun Pharmaceutical", exchange: "NSE", sector: "Pharma", approximatePrice: 1700),
        PopularStock(symbol: "DRREDDY", name: "Dr. Reddy's Laboratories", exchange: "NSE", sector: "Pharma", approximatePrice: 1300),
        PopularStock(symbol: "CIPLA", name: "Cipla Limited", exchange: "NSE", sector: "Pharma", approximatePrice: 1500),
        
        // Oil & Gas
        PopularStock(symbol: "RELIANCE", name: "Reliance Industries", exchange: "NSE", sector: "Oil & Gas", approximatePrice: 2900),
        PopularStock(symbol: "ONGC", name: "Oil & Natural Gas Corp", exchange: "NSE", sector: "Oil & Gas", approximatePrice: 300),
        PopularStock(symbol: "IOC", name: "Indian Oil Corporation", exchange: "NSE", sector: "Oil & Gas", approximatePrice: 150),
        
        // Metals & Mining
        PopularStock(symbol: "TATASTEEL", name: "Tata Steel", exchange: "NSE", sector: "Metals", approximatePrice: 140),
        PopularStock(symbol: "HINDALCO", name: "Hindalco Industries", exchange: "NSE", sector: "Metals", approximatePrice: 600),
        PopularStock(symbol: "JSWSTEEL", name: "JSW Steel", exchange: "NSE", sector: "Metals", approximatePrice: 900),
        
        // Telecom
        PopularStock(symbol: "BHARTIARTL", name: "Bharti Airtel", exchange: "NSE", sector: "Telecom", approximatePrice: 1500),
        PopularStock(symbol: "JIOFINANCE", name: "Jio Financial Services", exchange: "NSE", sector: "Telecom", approximatePrice: 300),
        
        // Power & Utilities
        PopularStock(symbol: "NTPC", name: "NTPC Limited", exchange: "NSE", sector: "Power", approximatePrice: 350),
        PopularStock(symbol: "POWERGRID", name: "Power Grid Corp", exchange: "NSE", sector: "Power", approximatePrice: 320),
        
        // Cement
        PopularStock(symbol: "ULTRACEMCO", name: "UltraTech Cement", exchange: "NSE", sector: "Cement", approximatePrice: 11000),
        PopularStock(symbol: "SHREECEM", name: "Shree Cement", exchange: "NSE", sector: "Cement", approximatePrice: 27000),
        
        // Consumer Goods
        PopularStock(symbol: "ASIANPAINT", name: "Asian Paints", exchange: "NSE", sector: "Paints", approximatePrice: 2900),
        PopularStock(symbol: "TITAN", name: "Titan Company", exchange: "NSE", sector: "Jewellery", approximatePrice: 3400)
    ]
}

#Preview {
    StockPickerView(portfolioViewModel: PortfolioViewModel())
} 