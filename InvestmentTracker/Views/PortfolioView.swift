import SwiftUI

// MARK: - Supporting Data Structures
// Note: Time frame functionality removed as per requirements - showing current snapshot only

// MARK: - Supporting View Components
struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
            )
        }
    }
}

struct AssetClassCard: View {
    let type: InvestmentType
    let value: Double
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: type.icon)
                    .font(.title3)
                    .foregroundColor(color)
                
                Spacer()
                
                if value > 0 {
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Text(type.rawValue)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Text(formatLargeCurrency(value))
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(value > 0 ? .primary : .secondary)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(color.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct PerformanceMetricRow: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Circle()
                .fill(color.opacity(0.2))
                .frame(width: 8, height: 8)
            
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
        }
    }
}

// MARK: - Main Portfolio View
struct PortfolioView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 24) {
                    // Hero Portfolio Card
                    portfolioSummaryCard
                    
                    // Quick Actions
                    quickActionsSection
                    
                    // Asset Allocation Chart
                    assetAllocationSection
                    
                    // Asset Classes Grid
                    assetClassesGrid
                    
                    // Performance Insights
                    performanceInsightsSection
                    
                    // Development Helper
                    developmentSection
                }
                .padding(.horizontal)
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("Portfolio")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                portfolioViewModel.refreshData()
            }
            .onAppear {
                portfolioViewModel.loadAllData()
            }
        }
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "INR"
        formatter.currencySymbol = "₹"
        return formatter.string(from: NSNumber(value: amount)) ?? "₹0"
    }
    
    private func getValueForType(_ type: InvestmentType) -> Double {
        switch type {
        case .stocks:
            return portfolioViewModel.stocksValue
        case .mutualFunds:
            return portfolioViewModel.mutualFundsValue
        case .crypto:
            return portfolioViewModel.cryptoValue
        case .fixedDeposits:
            return portfolioViewModel.fixedDepositsValue
        case .epf:
            return portfolioViewModel.epfValue
        case .nps:
            return portfolioViewModel.npsValue
        case .rsu:
            return portfolioViewModel.rsuValue
        case .gold:
            return portfolioViewModel.goldValue
        }
    }
    
    private func getColorForType(_ type: InvestmentType) -> Color {
        switch type {
        case .stocks: return .blue
        case .mutualFunds: return .green
        case .crypto: return .orange
        case .fixedDeposits: return .purple
        case .epf: return .indigo
        case .nps: return .teal
        case .rsu: return .pink
        case .gold: return .yellow
        }
    }
}

// MARK: - Portfolio Summary Card
extension PortfolioView {
    private var portfolioSummaryCard: some View {
        VStack(spacing: 20) {
            // Main Value
            VStack(spacing: 8) {
                Text("Total Portfolio Value")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text(formatLargeCurrency(portfolioViewModel.totalPortfolioValue))
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
            }
            
            // P&L Summary
            HStack(spacing: 40) {
                VStack(spacing: 4) {
                    Text("Total P&L")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    let plColor = portfolioViewModel.totalProfitLoss >= 0 ? Color.green : Color.red
                    Text(formatCurrency(portfolioViewModel.totalProfitLoss))
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(plColor)
                }
                
                VStack(spacing: 4) {
                    Text("P&L %")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    let plColor = portfolioViewModel.totalProfitLoss >= 0 ? Color.green : Color.red
                    Text(String(format: "%.2f%%", portfolioViewModel.totalProfitLossPercentage))
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(plColor)
                }
                
                VStack(spacing: 4) {
                    Text("Invested")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(formatLargeCurrency(portfolioViewModel.totalInvestedAmount))
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 4)
        )
    }
}

// MARK: - Time Frame Selector Removed
// Note: Time frame functionality removed as per requirements - showing current snapshot only

// MARK: - Quick Actions
extension PortfolioView {
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 12) {
                QuickActionButton(
                    title: "Add Investment",
                    icon: "plus.circle.fill",
                    color: .blue
                ) {
                    // TODO: Add investment action
                }
                
                QuickActionButton(
                    title: "View Reports",
                    icon: "chart.bar.fill",
                    color: .green
                ) {
                    // TODO: View reports action
                }
                
                QuickActionButton(
                    title: "Export Data",
                    icon: "square.and.arrow.up",
                    color: .orange
                ) {
                    // TODO: Export data action
                }
            }
        }
    }
}

// MARK: - Asset Allocation Section
extension PortfolioView {
    private var assetAllocationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Asset Allocation")
                .font(.headline)
                .fontWeight(.semibold)
            
            // Pie Chart Placeholder with Asset Legend
            HStack(spacing: 20) {
                // Simple Pie Chart Representation
                pieChartView
                    .frame(width: 150, height: 150)
                
                // Legend
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(InvestmentType.allCases.prefix(5), id: \.self) { type in
                        let value = getValueForType(type)
                        if value > 0 {
                            HStack(spacing: 8) {
                                Circle()
                                    .fill(getColorForType(type))
                                    .frame(width: 10, height: 10)
                                
                                Text(type.rawValue)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                Spacer()
                                
                                Text(formatPercentage(percentageOf(value: value, total: portfolioViewModel.totalPortfolioValue)))
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                        }
                    }
                }
                
                Spacer()
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
        )
    }
    
    private var pieChartView: some View {
        ZStack {
            // Background circle
            Circle()
                .stroke(Color(.systemGray5), lineWidth: 8)
            
            // Colored segments (simplified representation)
            ForEach(Array(InvestmentType.allCases.enumerated()), id: \.offset) { index, type in
                let value = getValueForType(type)
                if value > 0 {
                    Circle()
                        .trim(from: 0, to: CGFloat(value / portfolioViewModel.totalPortfolioValue))
                        .stroke(getColorForType(type), lineWidth: 8)
                        .rotationEffect(.degrees(Double(index) * 45))
                }
            }
            
            // Center text
            VStack(spacing: 2) {
                Text("Total")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Text(formatLargeCurrency(portfolioViewModel.totalPortfolioValue))
                    .font(.caption)
                    .fontWeight(.semibold)
            }
        }
    }
}

// MARK: - Asset Classes Grid
extension PortfolioView {
    private var assetClassesGrid: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Asset Classes")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                ForEach(InvestmentType.allCases, id: \.self) { type in
                    AssetClassCard(
                        type: type,
                        value: getValueForType(type),
                        color: getColorForType(type)
                    )
                }
            }
        }
    }
}

// MARK: - Performance Insights
extension PortfolioView {
    private var performanceInsightsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Performance Insights")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                PerformanceMetricRow(
                    title: "Best Performer",
                    value: getBestPerformer(),
                    color: .green
                )
                
                PerformanceMetricRow(
                    title: "Largest Holding",
                    value: getLargestHolding(),
                    color: .blue
                )
                
                PerformanceMetricRow(
                    title: "Most Diversified",
                    value: "8 Asset Classes",
                    color: .purple
                )
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
            )
        }
    }
    
    private func getBestPerformer() -> String {
        let assetReturns = InvestmentType.allCases.compactMap { type -> (String, Double)? in
            let invested = getInvestedForType(type)
            let current = getValueForType(type)
            guard invested > 0 else { return nil }
            let returnPct = ((current - invested) / invested) * 100
            return (type.rawValue, returnPct)
        }
        
        if let best = assetReturns.max(by: { $0.1 < $1.1 }) {
            return "\(best.0) (+\(String(format: "%.1f", best.1))%)"
        }
        return "No data"
    }
    
    private func getLargestHolding() -> String {
        let largest = InvestmentType.allCases.max { getValueForType($0) < getValueForType($1) }
        return largest?.rawValue ?? "No data"
    }
    
    private func getInvestedForType(_ type: InvestmentType) -> Double {
        switch type {
        case .stocks: return portfolioViewModel.stocksInvested
        case .mutualFunds: return portfolioViewModel.mutualFundsInvested
        case .crypto: return portfolioViewModel.cryptoInvested
        case .fixedDeposits: return portfolioViewModel.fixedDepositsInvested
        case .epf: return portfolioViewModel.epfInvested
        case .nps: return portfolioViewModel.npsInvested
        case .rsu: return portfolioViewModel.rsuInvested
        case .gold: return portfolioViewModel.goldInvested
        }
    }
}

// MARK: - Development Section
extension PortfolioView {
    private var developmentSection: some View {
        VStack(spacing: 12) {
            Text("Development Tools")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                Button("Load Sample Data") {
                    portfolioViewModel.loadSampleData()
                }
                .buttonStyle(.borderedProminent)
                
                Button("Clear All Data") {
                    portfolioViewModel.clearAllData()
                }
                .buttonStyle(.bordered)
                
                Button("Save Data") {
                    portfolioViewModel.saveAllData()
                }
                .buttonStyle(.bordered)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

#Preview {
    PortfolioView(portfolioViewModel: PortfolioViewModel())
} 