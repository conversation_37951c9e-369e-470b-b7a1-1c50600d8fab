import SwiftUI

struct GoldView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    @State private var showingAddSheet = false
    @State private var selectedGold: Gold?
    @State private var showingEditSheet = false
    @State private var showingDeleteAlert = false
    @State private var goldToDelete: Gold?
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    if portfolioViewModel.goldInvestments.isEmpty {
                        // Empty state
                        VStack(spacing: 20) {
                            Image(systemName: "circle.fill")
                                .font(.system(size: 60))
                                .foregroundColor(.yellow)
                                .padding()
                            
                            Text("No Gold Investments Yet")
                                .font(.title2)
                                .fontWeight(.semibold)
                            
                            Text("Add your first gold investment to start tracking")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .padding(.bottom, 20)
                            
                            Button("Add Gold Investment") {
                                showingAddSheet = true
                            }
                            .buttonStyle(.borderedProminent)
                        }
                        .padding(.top, 50)
                    } else {
                        // Gold Summary Card
                        GoldSummaryCard(goldInvestments: portfolioViewModel.goldInvestments)
                        
                        // Individual Gold Cards
                        ForEach(portfolioViewModel.goldInvestments, id: \.id) { gold in
                            GoldInvestmentCard(
                                goldInvestment: gold,
                                onEdit: {
                                    selectedGold = gold
                                    showingEditSheet = true
                                },
                                onDelete: {
                                    goldToDelete = gold
                                    showingDeleteAlert = true
                                }
                            )
                        }
                    }
                }
                .padding(.horizontal)
            }
            .navigationTitle("Gold Investments")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add") {
                        showingAddSheet = true
                    }
                    .foregroundColor(.yellow)
                }
            }
            .sheet(isPresented: $showingAddSheet) {
                AddEditGoldView(
                    portfolioViewModel: portfolioViewModel,
                    editingGold: nil
                )
            }
            .sheet(isPresented: $showingEditSheet) {
                AddEditGoldView(
                    portfolioViewModel: portfolioViewModel,
                    editingGold: selectedGold
                )
            }
            .alert("Delete Gold Investment", isPresented: $showingDeleteAlert) {
                Button("Delete", role: .destructive) {
                    if let gold = goldToDelete {
                        portfolioViewModel.deleteGold(gold)
                    }
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                if let gold = goldToDelete {
                    Text("Are you sure you want to delete the gold investment \"\(gold.name)\"?")
                }
            }
            .onAppear {
                portfolioViewModel.loadAllData()
            }
        }
    }
}

// MARK: - Gold Summary Card
struct GoldSummaryCard: View {
    let goldInvestments: [Gold]
    
    private var totalInvested: Double {
        goldInvestments.reduce(0) { $0 + $1.investedAmount }
    }
    
    private var totalCurrentValue: Double {
        goldInvestments.reduce(0) { $0 + $1.currentValue }
    }
    
    private var totalGrams: Double {
        goldInvestments.reduce(0) { $0 + $1.grams }
    }
    
    private var totalProfitLoss: Double {
        totalCurrentValue - totalInvested
    }
    
    private var totalProfitLossPercentage: Double {
        guard totalInvested > 0 else { return 0 }
        return (totalProfitLoss / totalInvested) * 100
    }
    
    private var kt22Count: Int {
        goldInvestments.filter { $0.purity == .kt22 }.count
    }
    
    private var kt24Count: Int {
        goldInvestments.filter { $0.purity == .kt24 }.count
    }
    
    var body: some View {
        VStack(spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Gold Portfolio Overview")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("\(goldInvestments.count) Gold Investments")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(CurrencyFormatter.shared.format(totalCurrentValue))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Current Value")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Divider()
            
            // Statistics Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                GoldStatCard(
                    title: "Total Invested",
                    value: CurrencyFormatter.shared.format(totalInvested),
                    icon: "arrow.down.circle.fill",
                    color: .yellow
                )
                
                GoldStatCard(
                    title: "Total P&L",
                    value: CurrencyFormatter.shared.format(totalProfitLoss),
                    icon: totalProfitLoss >= 0 ? "arrow.up.circle.fill" : "arrow.down.circle.fill",
                    color: totalProfitLoss >= 0 ? .green : .red
                )
                
                GoldStatCard(
                    title: "Total Weight",
                    value: "\(String(format: "%.2f", totalGrams))g",
                    icon: "scalemass.fill",
                    color: .orange
                )
                
                GoldStatCard(
                    title: "22kt / 24kt",
                    value: "\(kt22Count) / \(kt24Count)",
                    icon: "sparkles",
                    color: .blue
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Enhanced Gold Card
struct GoldInvestmentCard: View {
    let goldInvestment: Gold
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    private var profitLoss: Double {
        goldInvestment.currentValue - goldInvestment.investedAmount
    }
    
    private var profitLossPercentage: Double {
        guard goldInvestment.investedAmount > 0 else { return 0 }
        return (profitLoss / goldInvestment.investedAmount) * 100
    }
    
    private var isProfit: Bool {
        profitLoss >= 0
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with name and actions
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(goldInvestment.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    HStack {
                        Text(goldInvestment.purity.displayName)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.yellow.opacity(0.2))
                            .foregroundColor(.yellow)
                            .cornerRadius(4)
                        
                        Text("\(String(format: "%.2f", goldInvestment.grams))g")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                HStack(spacing: 8) {
                    Button(action: onEdit) {
                        Image(systemName: "pencil.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                    
                    Button(action: onDelete) {
                        Image(systemName: "trash.circle.fill")
                            .font(.title2)
                            .foregroundColor(.red)
                    }
                }
            }
            
            // Investment details
            VStack(spacing: 8) {
                HStack {
                    Text("Current Value:")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(CurrencyFormatter.shared.format(goldInvestment.currentValue))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("Invested Amount:")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(CurrencyFormatter.shared.format(goldInvestment.investedAmount))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("P&L:")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Spacer()
                    VStack(alignment: .trailing, spacing: 2) {
                        Text(CurrencyFormatter.shared.format(profitLoss))
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(isProfit ? .green : .red)
                        
                        Text("(\(isProfit ? "+" : "")\(String(format: "%.2f", profitLossPercentage))%)")
                            .font(.caption)
                            .foregroundColor(isProfit ? .green : .red)
                    }
                }
                
                Divider()
                
                HStack {
                    Text("Purchase Rate:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(CurrencyFormatter.shared.format(goldInvestment.calculatedPurchaseRate) + "/g")
                        .font(.caption)
                }
                
                HStack {
                    Text("Current Rate:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(CurrencyFormatter.shared.format(goldInvestment.currentRate) + "/g")
                        .font(.caption)
                }
                
                HStack {
                    Text("Investment Date:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(DateHelper.shared.format(goldInvestment.investmentDate))
                        .font(.caption)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - Gold Stat Card (Reusable component)
struct GoldStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title3)
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.headline)
                    .fontWeight(.bold)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.secondarySystemBackground))
        )
    }
}

#Preview {
    GoldView(portfolioViewModel: PortfolioViewModel())
} 