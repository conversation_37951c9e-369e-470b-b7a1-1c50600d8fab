import SwiftUI

struct EPFView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    @State private var showingAddSheet = false
    @State private var selectedEPF: EPF?
    @State private var showingEditSheet = false
    @State private var showingDeleteAlert = false
    @State private var epfToDelete: EPF?
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    if portfolioViewModel.epf.isEmpty {
                        // Empty state
                        VStack(spacing: 20) {
                            Image(systemName: "person.crop.circle.fill")
                                .font(.system(size: 60))
                                .foregroundColor(.green)
                                .padding()
                            
                            Text("No EPF Records Yet")
                                .font(.title2)
                                .fontWeight(.semibold)
                            
                            Text("Add your EPF balance to start tracking")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .padding(.bottom, 20)
                            
                            Button("Add EPF Entry") {
                                showingAddSheet = true
                            }
                            .buttonStyle(.borderedProminent)
                        }
                        .padding(.top, 50)
                    } else {
                        // EPF Summary Card
                        EPFSummaryCard(epfEntries: portfolioViewModel.epf)
                        
                        // Individual EPF Cards
                        ForEach(portfolioViewModel.epf, id: \.id) { epf in
                            EnhancedEPFCard(
                                epf: epf,
                                onEdit: {
                                    selectedEPF = epf
                                    showingEditSheet = true
                                },
                                onDelete: {
                                    epfToDelete = epf
                                    showingDeleteAlert = true
                                }
                            )
                        }
                    }
                }
                .padding(.horizontal)
            }
            .navigationTitle("EPF")
            .toolbar {
                if portfolioViewModel.epf.isEmpty {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Add") {
                            showingAddSheet = true
                        }
                        .foregroundColor(.green)
                    }
                }
            }
            .sheet(isPresented: $showingAddSheet) {
                AddEditEPFView(
                    portfolioViewModel: portfolioViewModel,
                    editingEPF: nil
                )
            }
            .sheet(isPresented: $showingEditSheet) {
                AddEditEPFView(
                    portfolioViewModel: portfolioViewModel,
                    editingEPF: selectedEPF
                )
            }
            .alert("Delete EPF Entry", isPresented: $showingDeleteAlert) {
                Button("Delete", role: .destructive) {
                    if let epf = epfToDelete {
                        portfolioViewModel.deleteEPF(epf)
                    }
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                if epfToDelete != nil {
                    Text("Are you sure you want to delete this EPF entry?")
                }
            }
            .onAppear {
                portfolioViewModel.loadAllData()
            }
        }
    }
}

// MARK: - EPF Summary Card
struct EPFSummaryCard: View {
    let epfEntries: [EPF]
    
    private var totalInvested: Double {
        epfEntries.reduce(0) { $0 + $1.investedAmount }
    }
    
    private var totalCurrentValue: Double {
        epfEntries.reduce(0) { $0 + $1.currentValue }
    }
    
    private var totalProfitLoss: Double {
        totalCurrentValue - totalInvested
    }
    
    private var profitLossPercentage: Double {
        guard totalInvested > 0 else { return 0 }
        return (totalProfitLoss / totalInvested) * 100
    }
    
    var body: some View {
        VStack(spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("EPF Account")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("Employee Provident Fund")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(CurrencyFormatter.shared.format(totalCurrentValue))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Current Value")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Divider()
            
            // Statistics Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                StatCard(
                    title: "Total Invested",
                    value: CurrencyFormatter.shared.format(totalInvested),
                    icon: "arrow.down.circle.fill",
                    color: .blue
                )
                
                StatCard(
                    title: "Profit/Loss",
                    value: CurrencyFormatter.shared.format(totalProfitLoss),
                    icon: totalProfitLoss >= 0 ? "arrow.up.circle.fill" : "arrow.down.circle.fill",
                    color: totalProfitLoss >= 0 ? .green : .red
                )
            }
            
            // P&L Percentage
            HStack {
                Text("Returns")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(profitLossPercentage >= 0 ? "+" : "")\(String(format: "%.2f", profitLossPercentage))%")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(profitLossPercentage >= 0 ? .green : .red)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Enhanced EPF Card
struct EnhancedEPFCard: View {
    let epf: EPF
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    private var profitLoss: Double {
        epf.currentValue - epf.investedAmount
    }
    
    private var profitLossPercentage: Double {
        guard epf.investedAmount > 0 else { return 0 }
        return (profitLoss / epf.investedAmount) * 100
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with EPF name and actions
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Employee Provident Fund")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("Last updated \(DateHelper.shared.format(epf.lastUpdated, style: .short))")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Menu {
                    Button("Edit", action: onEdit)
                    Button("Delete", role: .destructive, action: onDelete)
                } label: {
                    Image(systemName: "ellipsis.circle")
                        .font(.title2)
                        .foregroundColor(.secondary)
                }
            }
            
            // Current Value
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Current Balance")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(CurrencyFormatter.shared.format(epf.currentValue))
                        .font(.title2)
                        .fontWeight(.bold)
                }
            }
            
            Divider()
            
            // Details Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                DetailItem(
                    title: "Total Invested",
                    value: CurrencyFormatter.shared.format(epf.investedAmount)
                )
                
                DetailItem(
                    title: "Profit/Loss",
                    value: CurrencyFormatter.shared.format(profitLoss)
                )
                
                DetailItem(
                    title: "Returns",
                    value: "\(profitLossPercentage >= 0 ? "+" : "")\(String(format: "%.2f", profitLossPercentage))%"
                )
                
                DetailItem(
                    title: "Updated On",
                    value: DateHelper.shared.format(epf.lastUpdated, style: .short)
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - Add/Edit EPF View
struct AddEditEPFView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    let editingEPF: EPF?
    
    @Environment(\.dismiss) private var dismiss
    
    @State private var investedAmount: String
    @State private var currentValue: String
    @State private var lastUpdated: Date
    
    @State private var showingError = false
    @State private var errorMessage = ""
    
    init(portfolioViewModel: PortfolioViewModel, editingEPF: EPF?) {
        self.portfolioViewModel = portfolioViewModel
        self.editingEPF = editingEPF
        
        if let epf = editingEPF {
            self._investedAmount = State(initialValue: String(epf.investedAmount))
            self._currentValue = State(initialValue: String(epf.currentValue))
            self._lastUpdated = State(initialValue: epf.investmentDate)
        } else {
            self._investedAmount = State(initialValue: "")
            self._currentValue = State(initialValue: "")
            self._lastUpdated = State(initialValue: Date())
        }
    }
    
    private var isEditing: Bool {
        editingEPF != nil
    }
    
    private var isFormValid: Bool {
        !investedAmount.isEmpty &&
        !currentValue.isEmpty &&
        Double(investedAmount) != nil &&
        Double(currentValue) != nil
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("EPF Details") {
                    HStack {
                        Text("₹")
                            .foregroundColor(.secondary)
                        TextField("Total Invested Amount", text: $investedAmount)
                            .keyboardType(.decimalPad)
                    }
                    
                    HStack {
                        Text("₹")
                            .foregroundColor(.secondary)
                        TextField("Current Balance", text: $currentValue)
                            .keyboardType(.decimalPad)
                    }
                    
                    DatePicker("Last Updated", selection: $lastUpdated, displayedComponents: .date)
                }
                
                if let invested = Double(investedAmount),
                   let current = Double(currentValue),
                   invested > 0 {
                    Section("Calculated Values") {
                        let profitLoss = current - invested
                        let percentage = (profitLoss / invested) * 100
                        
                        HStack {
                            Text("Profit/Loss")
                            Spacer()
                            Text(CurrencyFormatter.shared.format(profitLoss))
                                .foregroundColor(profitLoss >= 0 ? .green : .red)
                                .fontWeight(.semibold)
                        }
                        
                        HStack {
                            Text("Returns")
                            Spacer()
                            Text("\(percentage >= 0 ? "+" : "")\(String(format: "%.2f", percentage))%")
                                .foregroundColor(percentage >= 0 ? .green : .red)
                                .fontWeight(.semibold)
                        }
                    }
                }
            }
            .navigationTitle(isEditing ? "Edit EPF" : "Add EPF")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveEPF()
                    }
                    .disabled(!isFormValid)
                }
            }
            .alert("Error", isPresented: $showingError) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }

        }
    }
    

    
    private func saveEPF() {
        guard let invested = Double(investedAmount),
              let current = Double(currentValue) else {
            showError("Please enter valid amounts")
            return
        }
        
        guard invested > 0 && current > 0 else {
            showError("Amounts must be greater than 0")
            return
        }
        
        let epf = EPF(
            investedAmount: invested,
            currentValue: current,
            investmentDate: lastUpdated
        )
        
        if let editingEPF = editingEPF {
            portfolioViewModel.updateEPF(editingEPF, with: epf)
        } else {
            portfolioViewModel.addEPF(epf)
        }
        
        dismiss()
    }
    
    private func showError(_ message: String) {
        errorMessage = message
        showingError = true
    }
}

#Preview {
    EPFView(portfolioViewModel: PortfolioViewModel())
} 