import SwiftUI

struct RSUView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    @State private var showingAddSheet = false
    @State private var selectedRSU: RSU?
    @State private var showingEditSheet = false
    @State private var showingDeleteAlert = false
    @State private var rsuToDelete: RSU?
    
    private var totalInvested: Double {
        portfolioViewModel.rsus.reduce(0) { $0 + $1.investedAmount }
    }
    
    private var totalCurrentValue: Double {
        portfolioViewModel.rsus.reduce(0) { $0 + $1.currentValue }
    }
    
    private var totalProfitLoss: Double {
        totalCurrentValue - totalInvested
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                if portfolioViewModel.rsus.isEmpty {
                    // Empty State
                    Spacer()
                    
                    VStack(spacing: 20) {
                        Image(systemName: "star.circle")
                            .font(.system(size: 80))
                            .foregroundColor(.pink.opacity(0.3))
                        
                        VStack(spacing: 8) {
                            Text("No RSU Entries")
                                .font(.title2)
                                .fontWeight(.semibold)
                            
                            Text("Add your first RSU entry to start tracking your stock units and performance")
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 40)
                        }
                        
                        Button(action: {
                            showingAddSheet = true
                        }) {
                            HStack {
                                Image(systemName: "plus")
                                Text("Add RSU Entry")
                            }
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.pink)
                            .cornerRadius(12)
                        }
                    }
                    
                    Spacer()
                } else {
                    // Content with RSU entries
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            // Summary Cards
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                                StatCard(
                                    title: "Invested Value (INR)",
                                    value: CurrencyFormatter.shared.format(totalInvested),
                                    icon: "star.fill",
                                    color: .pink
                                )
                                
                                StatCard(
                                    title: "Current Value",
                                    value: CurrencyFormatter.shared.format(totalCurrentValue),
                                    icon: "chart.line.uptrend.xyaxis",
                                    color: .green
                                )
                            }
                            
                            // P&L Card
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("Total P&L")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                    
                                    Text(CurrencyFormatter.shared.format(totalProfitLoss))
                                        .font(.title2)
                                        .fontWeight(.bold)
                                        .foregroundColor(totalProfitLoss >= 0 ? .green : .red)
                                }
                                
                                Spacer()
                                
                                if totalInvested > 0 {
                                    VStack(alignment: .trailing, spacing: 4) {
                                        Text("Returns")
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        let percentage = (totalProfitLoss / totalInvested) * 100
                                        Text("\(percentage >= 0 ? "+" : "")\(String(format: "%.2f", percentage))%")
                                            .font(.title2)
                                            .fontWeight(.bold)
                                            .foregroundColor(percentage >= 0 ? .green : .red)
                                    }
                                }
                            }
                            .padding(16)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(.secondarySystemBackground))
                            )
                            
                            // RSU Entries
                            ForEach(portfolioViewModel.rsus) { rsu in
                                RSUDetailCard(
                                    rsu: rsu,
                                    onEdit: {
                                        selectedRSU = rsu
                                        showingEditSheet = true
                                    },
                                    onDelete: {
                                        rsuToDelete = rsu
                                        showingDeleteAlert = true
                                    }
                                )
                            }
                        }
                        .padding(16)
                    }
                }
            }
            .navigationTitle("RSU")
            .toolbar {
                if !portfolioViewModel.rsus.isEmpty {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(action: {
                            showingAddSheet = true
                        }) {
                            Image(systemName: "plus")
                        }
                    }
                }
            }
            .onAppear {
                portfolioViewModel.refreshData()
            }
        }
        .sheet(isPresented: $showingAddSheet) {
            AddEditRSUView(portfolioViewModel: portfolioViewModel, editingRSU: nil)
        }
        .sheet(isPresented: $showingEditSheet) {
            AddEditRSUView(portfolioViewModel: portfolioViewModel, editingRSU: selectedRSU)
        }
        .alert("Delete RSU Entry", isPresented: $showingDeleteAlert) {
            Button("Delete", role: .destructive) {
                if let rsu = rsuToDelete {
                    portfolioViewModel.deleteRSU(rsu)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            if rsuToDelete != nil {
                Text("Are you sure you want to delete this RSU entry?")
            }
        }
    }
}

// MARK: - RSU Card View
struct RSUDetailCard: View {
    let rsu: RSU
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    private var profitLoss: Double {
        rsu.currentValue - rsu.investedAmount
    }
    
    private var profitLossPercentage: Double {
        guard rsu.investedAmount > 0 else { return 0 }
        return (profitLoss / rsu.investedAmount) * 100
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(rsu.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("RSU")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Menu {
                    Button("Edit", action: onEdit)
                    Button("Delete", role: .destructive, action: onDelete)
                } label: {
                    Image(systemName: "ellipsis.circle")
                        .font(.title2)
                        .foregroundColor(.secondary)
                }
            }
            
            // Units and Values
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Units")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(String(format: "%.0f", rsu.numberOfStocks)) units")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("Grant Price (USD)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("$\(String(format: "%.2f", rsu.unitPriceUSD))")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("Current Price (USD)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("$\(String(format: "%.2f", rsu.currentPriceUSD))")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("Exchange Rate")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    VStack(alignment: .trailing) {
                        Text("₹\(String(format: "%.2f", rsu.exchangeRate))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("Live: ₹\(String(format: "%.2f", ExchangeRateService.shared.currentUSDToINRRate))")
                            .font(.caption2)
                            .foregroundColor(.green)
                    }
                }
            }
            
            Divider()
            
            // Details Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                DetailItem(
                    title: "Invested (USD)",
                    value: "$\(String(format: "%.2f", rsu.investedAmountUSD))"
                )
                
                DetailItem(
                    title: "Current (USD)",
                    value: "$\(String(format: "%.2f", rsu.numberOfStocks * rsu.currentPriceUSD))"
                )
                
                DetailItem(
                    title: "Current (INR)",
                    value: CurrencyFormatter.shared.format(rsu.currentAmountINR)
                )
                
                DetailItem(
                    title: "P&L (INR)",
                    value: CurrencyFormatter.shared.format(profitLoss)
                )
            }
            
            // Returns section
            HStack {
                Text("Returns")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(profitLossPercentage >= 0 ? "+" : "")\(String(format: "%.2f", profitLossPercentage))%")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(profitLossPercentage >= 0 ? .green : .red)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - Add/Edit RSU View
struct AddEditRSUView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    let editingRSU: RSU?
    
    @Environment(\.dismiss) private var dismiss
    
    @State private var companyName: String
    @State private var units: String
    @State private var unitPriceUSD: String
    @State private var currentPriceUSD: String
    @State private var exchangeRate: String
    @State private var grantDate: Date
    
    @State private var showingError = false
    @State private var errorMessage = ""
    
    init(portfolioViewModel: PortfolioViewModel, editingRSU: RSU?) {
        self.portfolioViewModel = portfolioViewModel
        self.editingRSU = editingRSU
        
        if let rsu = editingRSU {
            self._companyName = State(initialValue: rsu.name)
            self._units = State(initialValue: String(format: "%.0f", rsu.numberOfStocks))
            self._unitPriceUSD = State(initialValue: String(format: "%.2f", rsu.unitPriceUSD))
            self._currentPriceUSD = State(initialValue: String(format: "%.2f", rsu.currentPriceUSD))
            self._exchangeRate = State(initialValue: String(format: "%.2f", rsu.exchangeRate))
            self._grantDate = State(initialValue: rsu.investmentDate)
        } else {
            self._companyName = State(initialValue: "")
            self._units = State(initialValue: "")
            self._unitPriceUSD = State(initialValue: "")
            self._currentPriceUSD = State(initialValue: "")
            self._exchangeRate = State(initialValue: "83.50") // Default exchange rate
            self._grantDate = State(initialValue: Date())
        }
    }
    
    private var isEditing: Bool {
        editingRSU != nil
    }
    
    private var isFormValid: Bool {
        !companyName.isEmpty &&
        !units.isEmpty &&
        !unitPriceUSD.isEmpty &&
        !currentPriceUSD.isEmpty &&
        !exchangeRate.isEmpty &&
        Double(units) != nil &&
        Double(unitPriceUSD) != nil &&
        Double(currentPriceUSD) != nil &&
        Double(exchangeRate) != nil
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("Company Details") {
                    TextField("Company Name", text: $companyName)
                        .textInputAutocapitalization(.words)
                }
                
                Section("RSU Details") {
                    TextField("Number of Units", text: $units)
                        .keyboardType(.numberPad)
                    
                    HStack {
                        Text("$")
                            .foregroundColor(.secondary)
                        TextField("Unit Price at Grant (USD)", text: $unitPriceUSD)
                            .keyboardType(.decimalPad)
                    }
                    
                    HStack {
                        Text("$")
                            .foregroundColor(.secondary)
                        TextField("Current Price per Unit (USD)", text: $currentPriceUSD)
                            .keyboardType(.decimalPad)
                    }
                    
                    HStack {
                        Text("₹")
                            .foregroundColor(.secondary)
                        HStack {
                            TextField("Exchange Rate (USD to INR)", text: $exchangeRate)
                                .keyboardType(.decimalPad)
                            
                            Button(action: {
                                ExchangeRateService.shared.fetchLatestExchangeRateSync()
                                exchangeRate = String(format: "%.2f", ExchangeRateService.shared.currentUSDToINRRate)
                            }) {
                                Image(systemName: "arrow.clockwise")
                                    .foregroundColor(.blue)
                            }
                            .disabled(ExchangeRateService.shared.isLoading)
                        }
                    }
                    
                    DatePicker("Grant Date", selection: $grantDate, displayedComponents: .date)
                }
                
                if let unitsValue = Double(units),
                   let unitPriceVal = Double(unitPriceUSD),
                   let currentPriceVal = Double(currentPriceUSD),
                   let exchangeRateVal = Double(exchangeRate),
                   unitsValue > 0 && unitPriceVal > 0 && currentPriceVal > 0 && exchangeRateVal > 0 {
                    Section("Calculated Values") {
                        let investedAmountUSD = unitsValue * unitPriceVal
                        let currentAmountUSD = unitsValue * currentPriceVal
                        let investedAmountINR = investedAmountUSD * exchangeRateVal
                        let currentAmountINR = currentAmountUSD * exchangeRateVal
                        let profitLoss = currentAmountINR - investedAmountINR
                        let percentage = investedAmountINR > 0 ? (profitLoss / investedAmountINR) * 100 : 0
                        
                        HStack {
                            Text("Invested Amount (USD)")
                            Spacer()
                            Text("$\(String(format: "%.2f", investedAmountUSD))")
                                .fontWeight(.medium)
                        }
                        
                        HStack {
                            Text("Current Amount (USD)")
                            Spacer()
                            Text("$\(String(format: "%.2f", currentAmountUSD))")
                                .fontWeight(.medium)
                        }
                        
                        HStack {
                            Text("Invested Amount (INR)")
                            Spacer()
                            Text(CurrencyFormatter.shared.format(investedAmountINR))
                                .fontWeight(.medium)
                        }
                        
                        HStack {
                            Text("Current Amount (INR)")
                            Spacer()
                            Text(CurrencyFormatter.shared.format(currentAmountINR))
                                .fontWeight(.medium)
                        }
                        
                        HStack {
                            Text("Total P&L (INR)")
                            Spacer()
                            Text(CurrencyFormatter.shared.format(profitLoss))
                                .foregroundColor(profitLoss >= 0 ? .green : .red)
                                .fontWeight(.semibold)
                        }
                        
                        HStack {
                            Text("Returns")
                            Spacer()
                            Text("\(percentage >= 0 ? "+" : "")\(String(format: "%.2f", percentage))%")
                                .foregroundColor(percentage >= 0 ? .green : .red)
                                .fontWeight(.semibold)
                        }
                    }
                }
            }
            .navigationTitle(isEditing ? "Edit RSU" : "Add RSU")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveRSU()
                    }
                    .disabled(!isFormValid)
                }
            }
            .alert("Error", isPresented: $showingError) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }
        }
    }
    
    private func saveRSU() {
        guard let unitsValue = Double(units),
              let unitPriceVal = Double(unitPriceUSD),
              let currentPriceVal = Double(currentPriceUSD),
              let exchangeRateVal = Double(exchangeRate) else {
            showError("Please enter valid numbers")
            return
        }
        
        guard unitsValue > 0 && unitPriceVal > 0 && currentPriceVal > 0 && exchangeRateVal > 0 else {
            showError("All values must be greater than 0")
            return
        }
        
        let rsu = RSU(
            companyName: companyName,
            numberOfStocks: unitsValue,
            unitPriceUSD: unitPriceVal,
            currentPriceUSD: currentPriceVal,
            exchangeRate: exchangeRateVal,
            investmentDate: grantDate
        )
        
        if let editingRSU = editingRSU {
            portfolioViewModel.updateRSU(editingRSU, with: rsu)
        } else {
            portfolioViewModel.addRSU(rsu)
        }
        
        dismiss()
    }
    
    private func showError(_ message: String) {
        errorMessage = message
        showingError = true
    }
}

#Preview {
    RSUView(portfolioViewModel: PortfolioViewModel())
} 