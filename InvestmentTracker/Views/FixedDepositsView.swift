import SwiftUI

struct FixedDepositsView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    @State private var showingAddSheet = false
    @State private var selectedFD: FixedDeposit?
    @State private var showingEditSheet = false
    @State private var showingDeleteAlert = false
    @State private var fdToDelete: FixedDeposit?
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    if portfolioViewModel.fixedDeposits.isEmpty {
                        // Empty state
                        VStack(spacing: 20) {
                            Image(systemName: "building.columns.fill")
                                .font(.system(size: 60))
                                .foregroundColor(.blue)
                                .padding()
                            
                            Text("No Fixed Deposits Yet")
                                .font(.title2)
                                .fontWeight(.semibold)
                            
                            Text("Add your first FD to start tracking")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .padding(.bottom, 20)
                            
                            But<PERSON>("Add Fixed Deposit") {
                                showingAddSheet = true
                            }
                            .buttonStyle(.borderedProminent)
                        }
                        .padding(.top, 50)
                    } else {
                        // FD Summary Card
                        FDSummaryCard(fixedDeposits: portfolioViewModel.fixedDeposits)
                        
                        // Individual FD Cards
                        ForEach(portfolioViewModel.fixedDeposits, id: \.id) { fd in
                            EnhancedFDCard(
                                fixedDeposit: fd,
                                onEdit: {
                                    selectedFD = fd
                                    showingEditSheet = true
                                },
                                onDelete: {
                                    fdToDelete = fd
                                    showingDeleteAlert = true
                                }
                            )
                        }
                    }
                }
                .padding(.horizontal)
            }
            .navigationTitle("Fixed Deposits")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add") {
                        showingAddSheet = true
                    }
                    .foregroundColor(.blue)
                }
            }
            .sheet(isPresented: $showingAddSheet) {
                AddEditFDView(
                    portfolioViewModel: portfolioViewModel,
                    editingFD: nil
                )
            }
            .sheet(isPresented: $showingEditSheet) {
                AddEditFDView(
                    portfolioViewModel: portfolioViewModel,
                    editingFD: selectedFD
                )
            }
            .alert("Delete Fixed Deposit", isPresented: $showingDeleteAlert) {
                Button("Delete", role: .destructive) {
                    if let fd = fdToDelete {
                        portfolioViewModel.deleteFixedDeposit(fd)
                    }
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                if let fd = fdToDelete {
                    Text("Are you sure you want to delete the FD from \(fd.name)?")
                }
            }
            .onAppear {
                portfolioViewModel.loadAllData()
            }
        }
    }
}

// MARK: - FD Summary Card
struct FDSummaryCard: View {
    let fixedDeposits: [FixedDeposit]
    
    private var totalInvested: Double {
        fixedDeposits.reduce(0) { $0 + $1.investedAmount }
    }
    
    private var totalMaturityValue: Double {
        fixedDeposits.reduce(0) { $0 + $1.maturityAmount }
    }
    
    private var totalCurrentValue: Double {
        fixedDeposits.reduce(0) { $0 + $1.currentValue }
    }
    
    private var totalInterest: Double {
        totalMaturityValue - totalInvested
    }
    
    private var maturedFDs: Int {
        fixedDeposits.filter { $0.maturityDate <= Date() }.count
    }
    
    private var activeFDs: Int {
        fixedDeposits.filter { $0.maturityDate > Date() }.count
    }
    
    var body: some View {
        VStack(spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Portfolio Overview")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("\(fixedDeposits.count) Fixed Deposits")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                                            Text(CurrencyFormatter.shared.format(totalCurrentValue))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Current Value")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Divider()
            
            // Statistics Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                StatCard(
                    title: "Total Invested",
                                                value: CurrencyFormatter.shared.format(totalInvested),
                    icon: "arrow.down.circle.fill",
                    color: .blue
                )
                
                StatCard(
                    title: "Total Interest",
                                                value: CurrencyFormatter.shared.format(totalInterest),
                    icon: "plus.circle.fill",
                    color: .green
                )
                
                StatCard(
                    title: "Active FDs",
                    value: "\(activeFDs)",
                    icon: "clock.fill",
                    color: .orange
                )
                
                StatCard(
                    title: "Matured FDs",
                    value: "\(maturedFDs)",
                    icon: "checkmark.circle.fill",
                    color: .green
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Enhanced FD Card
struct EnhancedFDCard: View {
    let fixedDeposit: FixedDeposit
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    private var isMatured: Bool {
        fixedDeposit.maturityDate <= Date()
    }
    
    private var daysRemaining: Int {
        guard !isMatured else { return 0 }
        return Calendar.current.dateComponents([.day], from: Date(), to: fixedDeposit.maturityDate).day ?? 0
    }
    
    private var currentInterest: Double {
        fixedDeposit.currentInterest
    }
    
    private var progressPercentage: Double {
        let totalDays = Calendar.current.dateComponents([.day], from: fixedDeposit.investmentDate, to: fixedDeposit.maturityDate).day ?? 1
        let elapsedDays = Calendar.current.dateComponents([.day], from: fixedDeposit.investmentDate, to: Date()).day ?? 0
        return min(Double(elapsedDays) / Double(totalDays), 1.0)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with bank name and actions
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(fixedDeposit.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 8) {
                        Text("\(String(format: "%.2f", fixedDeposit.interestRate))% p.a.")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("•")
                            .foregroundColor(.secondary)
                        
                        Text(isMatured ? "Matured" : "Active")
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(isMatured ? Color.green : Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(4)
                    }
                }
                
                Spacer()
                
                Menu {
                    Button("Edit", action: onEdit)
                    Button("Delete", role: .destructive, action: onDelete)
                } label: {
                    Image(systemName: "ellipsis.circle")
                        .font(.title2)
                        .foregroundColor(.secondary)
                }
            }
            
            // Current Value and Progress
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Current Value")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(CurrencyFormatter.shared.format(fixedDeposit.currentValue))
                        .font(.title2)
                        .fontWeight(.bold)
                }
                
                if !isMatured {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("Progress")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Text("\(Int(progressPercentage * 100))%")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        
                        ProgressView(value: progressPercentage)
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                    }
                }
            }
            
            Divider()
            
            // Details Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                DetailItem(
                    title: "Principal",
                                                value: CurrencyFormatter.shared.format(fixedDeposit.investedAmount)
                )
                
                DetailItem(
                    title: "Maturity Amount",
                                                value: CurrencyFormatter.shared.format(fixedDeposit.maturityAmount)
                )
                
                DetailItem(
                    title: "Start Date",
                    value: DateHelper.shared.format(fixedDeposit.investmentDate, style: .short)
                )
                
                DetailItem(
                    title: "Maturity Date",
                    value: DateHelper.shared.format(fixedDeposit.maturityDate, style: .short)
                )
                
                DetailItem(
                    title: "Compounding",
                    value: fixedDeposit.compoundingFrequency.displayName
                )
                
                DetailItem(
                    title: "Total Interest",
                    value: CurrencyFormatter.shared.format(fixedDeposit.totalInterest)
                )
            }
            
            // Interest Information
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Interest Earned")
                        .font(.caption)
                        .foregroundColor(.secondary)
                                            Text(CurrencyFormatter.shared.format(currentInterest))
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.green)
                }
                
                Spacer()
                
                if !isMatured {
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("Days Remaining")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("\(daysRemaining) days")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - Add/Edit FD View
struct AddEditFDView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    let editingFD: FixedDeposit?
    
    @Environment(\.dismiss) private var dismiss
    
    @State private var bankName: String
    @State private var principalAmount: String
    @State private var interestRate: String
    @State private var startDate: Date
    @State private var maturityDate: Date
    @State private var compoundingFrequency: FixedDeposit.CompoundingFrequency
    
    @State private var showingError = false
    @State private var errorMessage = ""
    
    init(portfolioViewModel: PortfolioViewModel, editingFD: FixedDeposit?) {
        self.portfolioViewModel = portfolioViewModel
        self.editingFD = editingFD
        
        if let fd = editingFD {
            self._bankName = State(initialValue: fd.name)
            self._principalAmount = State(initialValue: String(fd.investedAmount))
            self._interestRate = State(initialValue: String(fd.interestRate))
            self._startDate = State(initialValue: fd.investmentDate)
            self._maturityDate = State(initialValue: fd.maturityDate)
            self._compoundingFrequency = State(initialValue: fd.compoundingFrequency)
        } else {
            self._bankName = State(initialValue: "")
            self._principalAmount = State(initialValue: "")
            self._interestRate = State(initialValue: "")
            self._startDate = State(initialValue: Date())
            self._maturityDate = State(initialValue: Date().addingTimeInterval(365 * 24 * 60 * 60)) // 1 year from now
            self._compoundingFrequency = State(initialValue: FixedDeposit.CompoundingFrequency.quarterly)
        }
    }
    
    private var isEditing: Bool {
        editingFD != nil
    }
    
    private var isFormValid: Bool {
        !bankName.isEmpty &&
        !principalAmount.isEmpty &&
        !interestRate.isEmpty &&
        Double(principalAmount) != nil &&
        Double(interestRate) != nil &&
        maturityDate > startDate
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("Bank Details") {
                    TextField("Bank Name", text: $bankName)
                        .textInputAutocapitalization(.words)
                }
                
                Section("Investment Details") {
                    HStack {
                        Text("₹")
                            .foregroundColor(.secondary)
                        TextField("Principal Amount", text: $principalAmount)
                            .keyboardType(.decimalPad)
                    }
                    
                    HStack {
                        TextField("Interest Rate", text: $interestRate)
                            .keyboardType(.decimalPad)
                        Text("% per annum")
                            .foregroundColor(.secondary)
                    }
                    
                    Picker("Compounding Frequency", selection: $compoundingFrequency) {
                        ForEach(FixedDeposit.CompoundingFrequency.allCases, id: \.self) { frequency in
                            Text(frequency.displayName).tag(frequency)
                        }
                    }
                }
                
                Section("Dates") {
                    DatePicker("Start Date", selection: $startDate, displayedComponents: .date)
                    DatePicker("Maturity Date", selection: $maturityDate, displayedComponents: .date)
                }
                
                if let principal = Double(principalAmount),
                   let rate = Double(interestRate),
                   principal > 0, rate > 0 {
                    Section("Calculated Values") {
                        let tempFD = FixedDeposit(
                            bankName: "temp",
                            investedAmount: principal,
                            interestRate: rate,
                            investmentDate: startDate,
                            maturityDate: maturityDate,
                            compoundingFrequency: compoundingFrequency
                        )
                        
                        HStack {
                            Text("Current Amount")
                            Spacer()
                            Text(CurrencyFormatter.shared.format(tempFD.currentValue))
                                .foregroundColor(.blue)
                                .fontWeight(.semibold)
                        }
                        
                        HStack {
                            Text("Maturity Amount")
                            Spacer()
                            Text(CurrencyFormatter.shared.format(tempFD.maturityAmount))
                                .foregroundColor(.indigo)
                                .fontWeight(.medium)
                        }
                        
                        HStack {
                            Text("Current Interest")
                            Spacer()
                            Text(CurrencyFormatter.shared.format(tempFD.currentInterest))
                                .foregroundColor(.green)
                        }
                        
                        HStack {
                            Text("Total Interest")
                            Spacer()
                            Text(CurrencyFormatter.shared.format(tempFD.totalInterest))
                                .foregroundColor(.mint)
                        }
                        
                        HStack {
                            Text("Duration")
                            Spacer()
                            let days = Calendar.current.dateComponents([.day], from: startDate, to: maturityDate).day ?? 0
                            Text("\(days) days")
                        }
                        
                        HStack {
                            Text("Compounding")
                            Spacer()
                            Text(compoundingFrequency.displayName)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            .navigationTitle(isEditing ? "Edit FD" : "Add FD")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveFD()
                    }
                    .disabled(!isFormValid)
                }
            }
            .alert("Error", isPresented: $showingError) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }

        }
    }
    

    
    private func saveFD() {
        guard let principal = Double(principalAmount),
              let rate = Double(interestRate) else {
            showError("Please enter valid amounts")
            return
        }
        
        guard maturityDate > startDate else {
            showError("Maturity date must be after start date")
            return
        }
        
        guard principal > 0 && rate > 0 else {
            showError("Principal and interest rate must be greater than 0")
            return
        }
        
        let fd = FixedDeposit(
            bankName: bankName,
            investedAmount: principal,
            interestRate: rate,
            investmentDate: startDate,
            maturityDate: maturityDate,
            compoundingFrequency: compoundingFrequency
        )
        
        if let editingFD = editingFD {
            portfolioViewModel.updateFixedDeposit(editingFD, with: fd)
        } else {
            portfolioViewModel.addFixedDeposit(fd)
        }
        
        dismiss()
    }
    
    private func showError(_ message: String) {
        errorMessage = message
        showingError = true
    }
}

// MARK: - Helper Views
struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(value)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.secondarySystemBackground))
        )
    }
}

struct DetailItem: View {
    let title: String
    let value: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
}

#Preview {
    FixedDepositsView(portfolioViewModel: PortfolioViewModel())
} 