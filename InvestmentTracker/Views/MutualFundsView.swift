import SwiftUI

struct MutualFundsView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    @StateObject private var mfNavService = MFNAVService()
    @State private var showingAddMutualFund = false
    @State private var selectedMutualFund: MutualFund?
    @State private var showingMutualFundPicker = false
    
    private var totalInvested: Double {
        portfolioViewModel.mutualFunds.reduce(0) { $0 + $1.investedAmount }
    }
    
    private var totalCurrentValue: Double {
        portfolioViewModel.mutualFunds.reduce(0) { $0 + $1.currentValue }
    }
    
    private var totalPnL: Double {
        totalCurrentValue - totalInvested
    }
    
    private var totalPnLPercentage: Double {
        guard totalInvested > 0 else { return 0 }
        return (totalPnL / totalInvested) * 100
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    if portfolioViewModel.mutualFunds.isEmpty {
                        // Empty state
                        VStack(spacing: 20) {
                            Image(systemName: "building.columns.fill")
                                .font(.system(size: 60))
                                .foregroundColor(.blue)
                                .padding()
                            
                            Text("No Mutual Funds Yet")
                                .font(.title2)
                                .fontWeight(.semibold)
                            
                            Text("Track your mutual fund investments with live NAV")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .padding(.bottom, 20)
                            
                            VStack(spacing: 12) {
                                Button("Add Mutual Fund") {
                                    showingAddMutualFund = true
                                }
                                .buttonStyle(.borderedProminent)
                                
                                Button("Choose from Popular Funds") {
                                    showingMutualFundPicker = true
                                }
                                .buttonStyle(.bordered)
                            }
                        }
                        .padding(.top, 50)
                    } else {
                        // Portfolio Summary
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("Portfolio Summary")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                
                                Spacer()
                                
                                Button(action: refreshAllNAVs) {
                                    Image(systemName: "arrow.clockwise")
                                        .foregroundColor(.blue)
                                }
                                .disabled(mfNavService.isLoading)
                            }
                            
                            VStack(spacing: 8) {
                                HStack {
                                    Text("Total Invested")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                    
                                    Spacer()
                                    
                                    Text(formatCurrency(totalInvested))
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                }
                                
                                HStack {
                                    Text("Current Value")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                    
                                    Spacer()
                                    
                                    Text(formatCurrency(totalCurrentValue))
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                }
                                
                                HStack {
                                    Text("Total P&L")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                    
                                    Spacer()
                                    
                                    VStack(alignment: .trailing, spacing: 2) {
                                        Text("\(totalPnL >= 0 ? "+" : "")\(formatCurrency(totalPnL))")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                            .foregroundColor(totalPnL >= 0 ? .green : .red)
                                        
                                        Text("(\(totalPnL >= 0 ? "+" : "")\(String(format: "%.2f", totalPnLPercentage))%)")
                                            .font(.caption)
                                            .foregroundColor(totalPnL >= 0 ? .green : .red)
                                    }
                                }
                            }
                            
                            if mfNavService.isLoading {
                                HStack {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                    Text("Refreshing NAVs...")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                        .padding(16)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemBackground))
                                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
                        )
                        
                        // Mutual funds list
                        ForEach(portfolioViewModel.mutualFunds, id: \.id) { fund in
                            MutualFundCard(
                                fund: fund,
                                mfNavService: mfNavService,
                                onRefresh: { refreshedFund in
                                    portfolioViewModel.updateMutualFundNAV(
                                        id: refreshedFund.id,
                                        newNAV: refreshedFund.currentNAV
                                    )
                                },
                                onEdit: { selectedMutualFund = fund },
                                onDelete: { portfolioViewModel.deleteMutualFund(id: fund.id) }
                            )
                        }
                    }
                }
                .padding(.horizontal)
            }
            .navigationTitle("Mutual Funds")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button(action: { showingAddMutualFund = true }) {
                            Label("Add Manually", systemImage: "plus")
                        }
                        
                        Button(action: { showingMutualFundPicker = true }) {
                            Label("Choose Popular Fund", systemImage: "star")
                        }
                        
                        if !portfolioViewModel.mutualFunds.isEmpty {
                            Button(action: refreshAllNAVs) {
                                Label("Refresh All NAVs", systemImage: "arrow.clockwise")
                            }
                        }
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .onAppear {
                portfolioViewModel.loadAllData()
            }
            .sheet(isPresented: $showingAddMutualFund) {
                AddEditMutualFundView(portfolioViewModel: portfolioViewModel, mutualFund: nil)
            }
            .sheet(item: $selectedMutualFund) { fund in
                AddEditMutualFundView(portfolioViewModel: portfolioViewModel, mutualFund: fund)
            }
            .sheet(isPresented: $showingMutualFundPicker) {
                MutualFundPickerView { selectedFund in
                    showingMutualFundPicker = false
                    // Pre-populate add form with selected fund
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        showingAddMutualFund = true
                    }
                }
            }
        }
    }
    
    private func refreshAllNAVs() {
        let schemeCodes = portfolioViewModel.mutualFunds.map { $0.schemeCode }
        
        Task {
            let navs = await mfNavService.fetchMultipleNAVs(schemeCodes: schemeCodes)
            
            await MainActor.run {
                for fund in portfolioViewModel.mutualFunds {
                    if let newNAV = navs[fund.schemeCode] {
                        portfolioViewModel.updateMutualFundNAV(id: fund.id, newNAV: newNAV)
                    }
                }
            }
        }
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "INR"
        formatter.currencySymbol = "₹"
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSNumber(value: amount)) ?? "₹0"
    }
}

struct MutualFundCard: View {
    let fund: MutualFund
    let mfNavService: MFNAVService
    let onRefresh: (MutualFund) -> Void
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    @State private var showingDeleteAlert = false
    
    private var profitLoss: Double {
        fund.currentValue - fund.investedAmount
    }
    
    private var profitLossPercentage: Double {
        guard fund.investedAmount > 0 else { return 0 }
        return (profitLoss / fund.investedAmount) * 100
    }
    
    private var isProfit: Bool {
        profitLoss >= 0
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(fund.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .lineLimit(2)
                    
                    HStack(spacing: 8) {
                        Text("Code: \(fund.schemeCode)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("Updated: \(fund.lastUpdated, style: .relative) ago")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Menu {
                    Button(action: refreshNAV) {
                        Label("Refresh NAV", systemImage: "arrow.clockwise")
                    }
                    .disabled(mfNavService.isLoading)
                    
                    Button(action: onEdit) {
                        Label("Edit", systemImage: "pencil")
                    }
                    
                    Button(role: .destructive, action: { showingDeleteAlert = true }) {
                        Label("Delete", systemImage: "trash")
                    }
                } label: {
                    Image(systemName: "ellipsis")
                        .foregroundColor(.secondary)
                        .padding(8)
                }
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(formatCurrency(fund.currentValue))
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 4) {
                        Image(systemName: isProfit ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        
                        Text("\(isProfit ? "+" : "")\(formatCurrency(profitLoss))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("(\(isProfit ? "+" : "")\(String(format: "%.2f", profitLossPercentage))%)")
                            .font(.caption)
                    }
                    .foregroundColor(isProfit ? .green : .red)
                }
                
                Spacer()
                
                Button(action: refreshNAV) {
                    Image(systemName: "arrow.clockwise")
                        .foregroundColor(.blue)
                }
                .disabled(mfNavService.isLoading)
            }
            
            Divider()
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Units")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(String(format: "%.4f", fund.units))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .center, spacing: 2) {
                    Text("Avg NAV")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("₹\(String(format: "%.4f", fund.purchaseNAV))")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("Current NAV")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("₹\(String(format: "%.4f", fund.currentNAV))")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Invested")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(formatCurrency(fund.investedAmount))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("Investment Date")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(fund.investmentDate, style: .date)
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .alert("Delete Mutual Fund", isPresented: $showingDeleteAlert) {
            Button("Delete", role: .destructive) {
                onDelete()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Are you sure you want to delete \(fund.name)? This action cannot be undone.")
        }
    }
    
    private func refreshNAV() {
        Task {
            if let newNAV = await mfNavService.fetchNAV(schemeCode: fund.schemeCode) {
                await MainActor.run {
                    var updatedFund = fund
                    updatedFund.updateCurrentNAV(newNAV)
                    onRefresh(updatedFund)
                }
            } else {
                // NAV fetch failed - the error message is already shown by the service
                // The user can manually edit the fund to update NAV
                print("Failed to fetch NAV for \(fund.name): \(mfNavService.errorMessage ?? "Unknown error")")
            }
        }
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "INR"
        formatter.currencySymbol = "₹"
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSNumber(value: amount)) ?? "₹0"
    }
}

#Preview {
    MutualFundsView(portfolioViewModel: PortfolioViewModel())
} 