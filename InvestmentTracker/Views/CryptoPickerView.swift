import SwiftUI

struct CryptoPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject private var cryptoService = CryptoPriceService.shared
    @State private var searchText = ""
    
    let onCoinSelected: (CryptoCoin, Double?) -> Void
    
    var body: some View {
        NavigationView {
            VStack {
                SearchBar(text: $searchText)
                    .onChange(of: searchText) { _, newValue in
                        Task {
                            await cryptoService.searchCoins(query: newValue)
                        }
                    }
                
                if cryptoService.isLoading {
                    ProgressView("Loading cryptocurrencies...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if let errorMessage = cryptoService.errorMessage {
                    VStack {
                        Image(systemName: "exclamationmark.triangle")
                            .foregroundColor(.orange)
                            .font(.largeTitle)
                        Text("Error Loading Data")
                            .font(.headline)
                            .padding(.top)
                        Text(errorMessage)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                            .padding()
                        Button("Try Again") {
                            Task {
                                await cryptoService.loadPopularCoins()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    List(cryptoService.searchResults, id: \.id) { coin in
                        CoinRow(coin: coin) {
                            Task {
                                // Fetch current price from CoinDCX when coin is selected
                                let currentPrice = await cryptoService.fetchCurrentPrice(for: coin.symbol)
                                await MainActor.run {
                                    onCoinSelected(coin, currentPrice)
                                    dismiss()
                                }
                            }
                        }
                    }
                }
            }
            .navigationTitle("Select Cryptocurrency")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden()
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct CoinRow: View {
    let coin: CryptoCoin
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(coin.name)
                        .font(.headline)
                        .foregroundColor(.primary)
                    Text(coin.symbol)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    if coin.currentPrice > 0 {
                        Text(CurrencyFormatter.shared.formatCryptoPrice(coin.currentPrice))
                            .font(.subheadline)
                            .foregroundColor(.primary)
                    } else {
                        Text("Tap to get price")
                            .font(.caption)
                            .foregroundColor(.blue)
                    }
                    
                    Text(coin.exchange)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    CryptoPickerView { coin, price in
        print("Selected: \(coin.name) at \(price ?? 0)")
    }
}