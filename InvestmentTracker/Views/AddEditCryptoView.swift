import SwiftUI

struct AddEditCryptoView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    @ObservedObject private var cryptoService = CryptoPriceService.shared
    
    let crypto: Crypto?
    
    @State private var searchText = ""
    @State private var name = ""
    @State private var symbol = ""
    @State private var exchange = "CoinDCX"
    @State private var quantity = ""
    @State private var purchasePrice = ""
    @State private var currentPrice = ""
    @State private var investmentDate = Date()
    
    @State private var isLoadingPrice = false
    @State private var priceErrorMessage: String?
    @State private var showingSearchResults = false
    
    private var isEditing: Bool {
        crypto != nil
    }
    
    private var availableExchanges: [String] {
        ["CoinDCX", "Binance", "WazirX", "CoinSwitch", "Other"]
    }
    
    private var canSave: Bool {
        !name.isEmpty && !symbol.isEmpty && !quantity.isEmpty && !purchasePrice.isEmpty && !currentPrice.isEmpty
    }
    
    private var quantityValue: Double {
        Double(quantity) ?? 0
    }
    
    private var purchasePriceValue: Double {
        Double(purchasePrice) ?? 0
    }
    
    private var currentPriceValue: Double {
        Double(currentPrice) ?? 0
    }
    
    private var investedAmount: Double {
        quantityValue * purchasePriceValue
    }
    
    private var currentValue: Double {
        quantityValue * currentPriceValue
    }
    
    private var profitLoss: Double {
        currentValue - investedAmount
    }
    
    private var profitLossPercentage: Double {
        guard investedAmount > 0 else { return 0 }
        return (profitLoss / investedAmount) * 100
    }
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Form Content
                ScrollView {
                    VStack(spacing: 20) {
                        // Crypto Search & Selection Section
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Cryptocurrency Details")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            VStack(spacing: 16) {
                                // Search Field
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Search Cryptocurrency")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    
                                    TextField("Search by name or symbol (e.g., Bitcoin, BTC)", text: $searchText)
                                        .textFieldStyle(.roundedBorder)
                                        .onChange(of: searchText) { _, newValue in
                                            if !newValue.isEmpty {
                                                showingSearchResults = true
                                                Task {
                                                    await cryptoService.searchCoins(query: newValue)
                                                }
                                            } else {
                                                showingSearchResults = false
                                            }
                                        }
                                }
                                
                                // Search Results
                                if showingSearchResults && !searchText.isEmpty {
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("Search Results")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        
                                        if cryptoService.isLoading {
                                            HStack {
                                                ProgressView()
                                                    .scaleEffect(0.8)
                                                Text("Searching...")
                                                    .font(.caption)
                                                    .foregroundColor(.secondary)
                                                Spacer()
                                            }
                                            .padding(.vertical, 8)
                                        } else if cryptoService.searchResults.isEmpty {
                                            Text("No cryptocurrencies found")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                                .padding(.vertical, 8)
                                        } else {
                                            ScrollView {
                                                LazyVStack(spacing: 8) {
                                                    ForEach(cryptoService.searchResults.prefix(5), id: \.symbol) { coin in
                                                        Button(action: {
                                                            selectCoin(coin)
                                                        }) {
                                                            HStack {
                                                                VStack(alignment: .leading, spacing: 2) {
                                                                    Text(coin.name)
                                                                        .font(.subheadline)
                                                                        .fontWeight(.medium)
                                                                        .foregroundColor(.primary)
                                                                    Text(coin.symbol.uppercased())
                                                                        .font(.caption)
                                                                        .foregroundColor(.secondary)
                                                                }
                                                                Spacer()
                                                                Image(systemName: "plus.circle.fill")
                                                                    .foregroundColor(.blue)
                                                            }
                                                            .padding(.horizontal, 12)
                                                            .padding(.vertical, 8)
                                                            .background(Color(.systemGray6))
                                                            .cornerRadius(8)
                                                        }
                                                    }
                                                }
                                            }
                                            .frame(maxHeight: 200)
                                        }
                                    }
                                }
                                
                                // Name Field (populated from search or manual)
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Cryptocurrency Name")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    
                                    TextField("e.g., Bitcoin", text: $name)
                                        .textFieldStyle(.roundedBorder)
                                }
                                
                                // Symbol Field (populated from search or manual)
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Symbol")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    
                                    TextField("e.g., BTC", text: $symbol)
                                        .textFieldStyle(.roundedBorder)
                                        .textInputAutocapitalization(.characters)
                                        .onChange(of: symbol) { _, newValue in
                                            // Auto-fetch price when symbol changes (and exchange is selected)
                                            if !newValue.isEmpty && !exchange.isEmpty {
                                                fetchCurrentPrice()
                                            }
                                        }
                                }
                                
                                // Exchange Selection
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Exchange")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    
                                    Picker("Exchange", selection: $exchange) {
                                        ForEach(availableExchanges, id: \.self) { exchange in
                                            Text(exchange).tag(exchange)
                                        }
                                    }
                                    .pickerStyle(.menu)
                                    .onChange(of: exchange) { _, newValue in
                                        // Auto-fetch price when exchange changes (and symbol is entered)
                                        if !symbol.isEmpty && newValue == "CoinDCX" {
                                            fetchCurrentPrice()
                                        }
                                    }
                                }
                                
                                // Current Price (Auto-populated)
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Current Price (₹)")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    
                                    HStack {
                                        if isLoadingPrice {
                                            ProgressView()
                                                .scaleEffect(0.8)
                                            Text("Fetching price...")
                                                .font(.subheadline)
                                                .foregroundColor(.secondary)
                                        } else if currentPrice.isEmpty {
                                            Text("Enter symbol and select exchange to fetch price")
                                                .font(.subheadline)
                                                .foregroundColor(.secondary)
                                        } else {
                                            Text(CurrencyFormatter.shared.formatCryptoPrice(Double(currentPrice) ?? 0))
                                                .font(.subheadline)
                                                .fontWeight(.medium)
                                                .foregroundColor(.primary)
                                        }
                                        
                                        Spacer()
                                        
                                        if !currentPrice.isEmpty && !isLoadingPrice {
                                            Text("Live from \(exchange)")
                                                .font(.caption)
                                                .foregroundColor(.green)
                                                .padding(.horizontal, 6)
                                                .padding(.vertical, 2)
                                                .background(Color.green.opacity(0.1))
                                                .cornerRadius(4)
                                        }
                                    }
                                    .padding()
                                    .background(Color(.systemGray6))
                                    .cornerRadius(8)
                                }
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                        
                        // Investment Details Section
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Investment Details")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            VStack(spacing: 16) {
                                // Quantity
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Quantity")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    
                                    TextField("0.00000000", text: $quantity)
                                        .textFieldStyle(.roundedBorder)
                                        .keyboardType(.decimalPad)
                                }
                                
                                // Purchase Price
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Average Purchase Price (₹)")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    
                                    TextField("0.00", text: $purchasePrice)
                                        .textFieldStyle(.roundedBorder)
                                        .keyboardType(.decimalPad)
                                }
                                
                                // Investment Date
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Investment Date")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    
                                    DatePicker("", selection: $investmentDate, displayedComponents: .date)
                                        .datePickerStyle(.compact)
                                }
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                        
                        // Investment Summary
                        if canSave {
                            VStack(alignment: .leading, spacing: 12) {
                                Text("Investment Summary")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                
                                VStack(spacing: 12) {
                                    HStack {
                                        Text("Invested Amount:")
                                            .font(.subheadline)
                                        Spacer()
                                        Text(CurrencyFormatter.shared.format(investedAmount))
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                    }
                                    
                                    HStack {
                                        Text("Current Value:")
                                            .font(.subheadline)
                                        Spacer()
                                        Text(CurrencyFormatter.shared.format(currentValue))
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                    }
                                    
                                    Divider()
                                    
                                    HStack {
                                        Text("Profit/Loss:")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        Spacer()
                                        VStack(alignment: .trailing, spacing: 2) {
                                            Text(CurrencyFormatter.shared.format(profitLoss))
                                                .font(.subheadline)
                                                .fontWeight(.semibold)
                                                .foregroundColor(profitLoss >= 0 ? .green : .red)
                                            Text("(\(String(format: "%.1f", profitLossPercentage))%)")
                                                .font(.caption)
                                                .foregroundColor(profitLoss >= 0 ? .green : .red)
                                        }
                                    }
                                }
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(.systemGray6))
                            )
                        }
                        
                        // Error Message
                        if let priceErrorMessage = priceErrorMessage {
                            Text(priceErrorMessage)
                                .foregroundColor(.red)
                                .font(.caption)
                                .padding()
                        }
                    }
                    .padding()
                }
                
                // Save Button
                VStack(spacing: 12) {
                    Button(action: saveCrypto) {
                        Text(isEditing ? "Update Crypto" : "Add Crypto")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(canSave ? Color.blue : Color.gray)
                            )
                    }
                    .disabled(!canSave)
                    .padding(.horizontal)
                    .padding(.bottom)
                }
                .background(Color(.systemBackground))
            }
            .navigationTitle(isEditing ? "Edit Crypto" : "Add Crypto")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                setupInitialValues()
            }
        }
    }
    
    private func selectCoin(_ coin: CryptoCoin) {
        name = coin.name
        symbol = coin.symbol.uppercased()
        searchText = ""
        showingSearchResults = false
        
        // Auto-fetch price if CoinDCX is selected
        if exchange == "CoinDCX" {
            fetchCurrentPrice()
        }
    }
    
    private func fetchCurrentPrice() {
        guard !symbol.isEmpty, exchange == "CoinDCX" else { return }
        
        isLoadingPrice = true
        priceErrorMessage = nil
        
        Task {
            let result = await cryptoService.fetchCurrentPriceWithDebug(for: symbol)
            
            await MainActor.run {
                isLoadingPrice = false
                
                if let price = result.price {
                    // Use appropriate precision for crypto prices
                    if price < 0.01 {
                        currentPrice = String(format: "%.8f", price)
                    } else if price < 1 {
                        currentPrice = String(format: "%.6f", price)
                    } else if price < 100 {
                        currentPrice = String(format: "%.4f", price)
                    } else {
                        currentPrice = String(format: "%.2f", price)
                    }
                } else {
                    priceErrorMessage = "Unable to fetch current price for \(symbol). Please enter manually."
                    currentPrice = ""
                }
            }
        }
    }
    
    private func setupInitialValues() {
        print("🔧 AddEditCryptoView setupInitialValues called")
        print("🔧 crypto parameter: \(crypto?.name ?? "nil")")
        print("🔧 isEditing: \(isEditing)")
        
        if let crypto = crypto {
            print("🔧 Setting up edit mode for: \(crypto.name)")
            name = crypto.name
            symbol = crypto.symbol
            exchange = crypto.exchange
            quantity = String(crypto.quantity)
            
            // Use appropriate precision for crypto prices
            if crypto.purchasePrice < 0.01 {
                purchasePrice = String(format: "%.8f", crypto.purchasePrice)
            } else if crypto.purchasePrice < 1 {
                purchasePrice = String(format: "%.6f", crypto.purchasePrice)
            } else if crypto.purchasePrice < 100 {
                purchasePrice = String(format: "%.4f", crypto.purchasePrice)
            } else {
                purchasePrice = String(format: "%.2f", crypto.purchasePrice)
            }
            
            if crypto.currentPrice < 0.01 {
                currentPrice = String(format: "%.8f", crypto.currentPrice)
            } else if crypto.currentPrice < 1 {
                currentPrice = String(format: "%.6f", crypto.currentPrice)
            } else if crypto.currentPrice < 100 {
                currentPrice = String(format: "%.4f", crypto.currentPrice)
            } else {
                currentPrice = String(format: "%.2f", crypto.currentPrice)
            }
            
            investmentDate = crypto.investmentDate
            print("🔧 Successfully populated edit form fields")
        } else {
            print("🔧 No crypto data - setting up add mode")
        }
    }
    
    private func saveCrypto() {
        let newCrypto = Crypto(
            name: name,
            symbol: symbol.uppercased(),
            exchange: exchange,
            quantity: quantityValue,
            purchasePrice: purchasePriceValue,
            currentPrice: currentPriceValue,
            investmentDate: investmentDate
        )
        
        if isEditing, let existingCrypto = crypto {
            portfolioViewModel.updateCrypto(existingCrypto.id, with: newCrypto)
        } else {
            portfolioViewModel.addCrypto(newCrypto)
        }
        
        dismiss()
    }
}

#Preview {
    AddEditCryptoView(portfolioViewModel: PortfolioViewModel(), crypto: nil)
} 