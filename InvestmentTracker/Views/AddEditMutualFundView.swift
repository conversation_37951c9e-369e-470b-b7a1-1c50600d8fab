import SwiftUI

struct AddEditMutualFundView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    @StateObject private var mfNavService = MFNAVService()
    
    let mutualFund: MutualFund?
    
    @State private var name = ""
    @State private var schemeCode = ""
    @State private var units = ""
    @State private var purchaseNAV = ""
    @State private var currentNAV = ""
    @State private var investmentDate = Date()
    
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var showingMFPicker = false
    
    private var isEditing: Bool {
        mutualFund != nil
    }
    
    private var isFormValid: Bool {
        !name.isEmpty &&
        !schemeCode.isEmpty &&
        !units.isEmpty &&
        !purchaseNAV.isEmpty &&
        !currentNAV.isEmpty &&
        Double(units) != nil &&
        Double(purchaseNAV) != nil &&
        Double(currentNAV) != nil
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Fund Details
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Fund Details")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        TextField("Fund Name", text: $name)
                            .textFieldStyle(.roundedBorder)
                        
                        HStack {
                            TextField("Scheme Code", text: $schemeCode)
                                .textFieldStyle(.roundedBorder)
                            
                            Button(action: { showingMFPicker = true }) {
                                Image(systemName: "magnifyingglass")
                                    .foregroundColor(.blue)
                            }
                        }
                        
                        HStack {
                            TextField("Current NAV", text: $currentNAV)
                                .keyboardType(.decimalPad)
                                .textFieldStyle(.roundedBorder)
                            
                            Button(action: fetchCurrentNAV) {
                                Image(systemName: "arrow.clockwise")
                                    .foregroundColor(.blue)
                            }
                            .disabled(schemeCode.isEmpty || mfNavService.isLoading)
                        }
                    }
                    
                    // Investment Details
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Investment Details")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        TextField("Units", text: $units)
                            .keyboardType(.decimalPad)
                            .textFieldStyle(.roundedBorder)
                        
                        TextField("Purchase NAV", text: $purchaseNAV)
                            .keyboardType(.decimalPad)
                            .textFieldStyle(.roundedBorder)
                        
                        DatePicker("Investment Date", selection: $investmentDate, displayedComponents: .date)
                    }
                    
                    // Loading indicator
                    if mfNavService.isLoading {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("Fetching live NAV...")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // Error message
                    if let errorMessage = mfNavService.errorMessage {
                        Text(errorMessage)
                            .font(.subheadline)
                            .foregroundColor(.red)
                    }
                    
                    // Investment Summary
                    if isFormValid {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Investment Summary")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            if let unitsValue = Double(units),
                               let purchaseNAVValue = Double(purchaseNAV),
                               let currentNAVValue = Double(currentNAV) {
                                
                                let invested = unitsValue * purchaseNAVValue
                                let current = unitsValue * currentNAVValue
                                let pnl = current - invested
                                let pnlPercentage = invested > 0 ? (pnl / invested) * 100 : 0
                                let isProfit = pnl >= 0
                                
                                VStack(spacing: 4) {
                                    HStack {
                                        Text("Total Invested")
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        Spacer()
                                        
                                        Text(formatCurrency(invested))
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                    }
                                    
                                    HStack {
                                        Text("Current Value")
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        Spacer()
                                        
                                        Text(formatCurrency(current))
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                    }
                                    
                                    HStack {
                                        Text("P&L")
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        Spacer()
                                        
                                        VStack(alignment: .trailing, spacing: 2) {
                                            Text("\(isProfit ? "+" : "")\(formatCurrency(pnl))")
                                                .font(.subheadline)
                                                .fontWeight(.medium)
                                                .foregroundColor(isProfit ? .green : .red)
                                            
                                            Text("(\(isProfit ? "+" : "")\(String(format: "%.2f", pnlPercentage))%)")
                                                .font(.caption)
                                                .foregroundColor(isProfit ? .green : .red)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                .padding()
            }
            .navigationTitle(isEditing ? "Edit Mutual Fund" : "Add Mutual Fund")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(isEditing ? "Update" : "Add") {
                        saveMutualFund()
                    }
                    .disabled(!isFormValid)
                }
            }
            .onAppear {
                loadMutualFundData()
            }
            .sheet(isPresented: $showingMFPicker) {
                MutualFundPickerView { selectedFund in
                    name = selectedFund.schemeName
                    schemeCode = selectedFund.schemeCode
                    showingMFPicker = false
                    
                    // Fetch current NAV for selected fund
                    Task {
                        if let nav = await mfNavService.fetchNAV(schemeCode: selectedFund.schemeCode) {
                            await MainActor.run {
                                currentNAV = String(format: "%.4f", nav)
                            }
                        } else {
                            await MainActor.run {
                                alertMessage = "Unable to fetch NAV for this fund. Please enter NAV manually."
                                showingAlert = true
                            }
                        }
                    }
                }
            }
        }
        .alert("Error", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private func loadMutualFundData() {
        if let mutualFund = mutualFund {
            name = mutualFund.name
            schemeCode = mutualFund.schemeCode
            units = String(format: "%.4f", mutualFund.units)
            purchaseNAV = String(format: "%.4f", mutualFund.purchaseNAV)
            currentNAV = String(format: "%.4f", mutualFund.currentNAV)
            investmentDate = mutualFund.investmentDate
        }
    }
    
    private func fetchCurrentNAV() {
        guard !schemeCode.isEmpty else { return }
        
        Task {
            if let nav = await mfNavService.fetchNAV(schemeCode: schemeCode) {
                await MainActor.run {
                    currentNAV = String(format: "%.4f", nav)
                }
            } else {
                await MainActor.run {
                    alertMessage = mfNavService.errorMessage ?? "Unable to fetch NAV. Please enter manually."
                    showingAlert = true
                }
            }
        }
    }
    
    private func saveMutualFund() {
        guard let unitsValue = Double(units),
              let purchaseNAVValue = Double(purchaseNAV),
              let currentNAVValue = Double(currentNAV) else {
            alertMessage = "Please enter valid numeric values for units and NAV."
            showingAlert = true
            return
        }
        
        if isEditing {
            portfolioViewModel.updateMutualFund(
                id: mutualFund!.id,
                name: name,
                schemeCode: schemeCode,
                units: unitsValue,
                purchaseNAV: purchaseNAVValue,
                currentNAV: currentNAVValue,
                investmentDate: investmentDate
            )
        } else {
            portfolioViewModel.addMutualFund(
                name: name,
                schemeCode: schemeCode,
                units: unitsValue,
                purchaseNAV: purchaseNAVValue,
                currentNAV: currentNAVValue,
                investmentDate: investmentDate
            )
        }
        
        dismiss()
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "INR"
        formatter.currencySymbol = "₹"
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSNumber(value: amount)) ?? "₹0"
    }
}

#Preview {
    AddEditMutualFundView(portfolioViewModel: PortfolioViewModel(), mutualFund: nil)
} 