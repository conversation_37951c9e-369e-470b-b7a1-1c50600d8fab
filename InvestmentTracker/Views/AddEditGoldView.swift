import SwiftUI

struct AddEditGoldView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    let editingGold: Gold?
    @Environment(\.dismiss) private var dismiss
    
    // Form fields
    @State private var name: String = ""
    @State private var purity: GoldPurity = .kt22
    @State private var grams: String = ""
    @State private var totalPrice: String = ""
    @State private var investmentDate = Date()
    
    // UI state
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isLoading = false
    
    // Gold price service
    @StateObject private var goldPriceService = GoldPriceService.shared
    
    private var isEditing: Bool {
        editingGold != nil
    }
    
    private var currentGoldPrice: Double {
        goldPriceService.getCurrentPrice(for: purity)
    }
    
    private var gramsValue: Double {
        Double(grams) ?? 0
    }
    
    private var totalPriceValue: Double {
        Double(totalPrice) ?? 0
    }
    
    private var calculatedPurchaseRate: Double {
        guard gramsValue > 0 else { return 0 }
        return totalPriceValue / gramsValue
    }
    
    private var currentValue: Double {
        gramsValue * currentGoldPrice
    }
    
    private var profitLoss: Double {
        currentValue - totalPriceValue
    }
    
    private var profitLossPercentage: Double {
        guard totalPriceValue > 0 else { return 0 }
        return (profitLoss / totalPriceValue) * 100
    }
    
    private var isFormValid: Bool {
        !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        gramsValue > 0 &&
        totalPriceValue > 0
    }
    
    init(portfolioViewModel: PortfolioViewModel, editingGold: Gold?) {
        self.portfolioViewModel = portfolioViewModel
        self.editingGold = editingGold
        
        if let gold = editingGold {
            self._name = State(initialValue: gold.name)
            self._purity = State(initialValue: gold.purity)
            self._grams = State(initialValue: String(gold.grams))
            self._totalPrice = State(initialValue: String(gold.totalPrice))
            self._investmentDate = State(initialValue: gold.investmentDate)
        }
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("Gold Investment Details") {
                    TextField("Investment Name", text: $name)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    Picker("Gold Purity", selection: $purity) {
                        ForEach(GoldPurity.allCases, id: \.self) { purity in
                            Text(purity.displayName).tag(purity)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    
                    HStack {
                        TextField("Weight in Grams", text: $grams)
                            .keyboardType(.decimalPad)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                        
                        if gramsValue > 0 {
                            Text("(\(String(format: "%.3f", gramsValue / 31.1035)) oz)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    TextField("Total Price Paid (₹)", text: $totalPrice)
                        .keyboardType(.decimalPad)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    if gramsValue > 0 && totalPriceValue > 0 {
                        HStack {
                            Text("Purchase Rate:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text("₹\(String(format: "%.2f", calculatedPurchaseRate))/gram")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                    }
                    
                    DatePicker("Investment Date", selection: $investmentDate, displayedComponents: .date)
                }
                
                Section("Current Gold Prices") {
                    VStack(spacing: 12) {
                        HStack {
                            Text("22kt Gold:")
                                .font(.subheadline)
                            Spacer()
                            Text("₹\(String(format: "%.2f", goldPriceService.currentPrice22kt))/gram")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(purity == .kt22 ? .yellow : .primary)
                        }
                        
                        HStack {
                            Text("24kt Gold:")
                                .font(.subheadline)
                            Spacer()
                            Text("₹\(String(format: "%.2f", goldPriceService.currentPrice24kt))/gram")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(purity == .kt24 ? .yellow : .primary)
                        }
                        
                        HStack {
                            Text("Last updated:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(DateHelper.shared.format(goldPriceService.lastUpdated, style: .short))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Button("Refresh Prices") {
                            refreshGoldPrices()
                        }
                        .disabled(isLoading)
                        .foregroundColor(.blue)
                        
                        if isLoading {
                            HStack {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("Updating prices...")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        if let errorMessage = goldPriceService.errorMessage {
                            Text(errorMessage)
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }
                }
                
                if isFormValid {
                    Section("Investment Summary") {
                        VStack(spacing: 12) {
                            HStack {
                                Text("Weight:")
                                Spacer()
                                Text("\(String(format: "%.2f", gramsValue)) grams")
                                    .fontWeight(.medium)
                            }
                            
                            HStack {
                                Text("Total Investment:")
                                Spacer()
                                Text(CurrencyFormatter.shared.format(totalPriceValue))
                                    .fontWeight(.medium)
                            }
                            
                            HStack {
                                Text("Current Value:")
                                Spacer()
                                Text(CurrencyFormatter.shared.format(currentValue))
                                    .fontWeight(.medium)
                            }
                            
                            Divider()
                            
                            HStack {
                                Text("Profit/Loss:")
                                Spacer()
                                VStack(alignment: .trailing, spacing: 2) {
                                    Text(CurrencyFormatter.shared.format(profitLoss))
                                        .fontWeight(.semibold)
                                        .foregroundColor(profitLoss >= 0 ? .green : .red)
                                    
                                    Text("(\(profitLoss >= 0 ? "+" : "")\(String(format: "%.2f", profitLossPercentage))%)")
                                        .font(.caption)
                                        .foregroundColor(profitLoss >= 0 ? .green : .red)
                                }
                            }
                            
                            HStack {
                                Text("Purchase Rate:")
                                Spacer()
                                Text("₹\(String(format: "%.2f", calculatedPurchaseRate))/gram")
                                    .fontWeight(.medium)
                            }
                            
                            HStack {
                                Text("Current Rate (\(purity.displayName)):")
                                Spacer()
                                Text("₹\(String(format: "%.2f", currentGoldPrice))/gram")
                                    .fontWeight(.medium)
                                    .foregroundColor(.yellow)
                            }
                        }
                        .padding(.vertical, 4)
                    }
                }
            }
            .navigationTitle(isEditing ? "Edit Gold Investment" : "Add Gold Investment")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(isEditing ? "Update" : "Add") {
                        saveGoldInvestment()
                    }
                    .disabled(!isFormValid)
                    .fontWeight(.semibold)
                }
            }
        }
        .alert("Error", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
        .onAppear {
            refreshGoldPrices()
        }
    }
    
    private func refreshGoldPrices() {
        isLoading = true
        goldPriceService.refreshGoldPrices()
        
        // Wait for the service to complete
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            isLoading = false
        }
    }
    
    private func saveGoldInvestment() {
        guard isFormValid else {
            alertMessage = "Please fill in all required fields with valid values."
            showingAlert = true
            return
        }
        
        let newGold = Gold(
            name: name.trimmingCharacters(in: .whitespacesAndNewlines),
            grams: gramsValue,
            purity: purity,
            purchaseRate: calculatedPurchaseRate,
            currentRate: currentGoldPrice,
            investmentDate: investmentDate,
            totalPrice: totalPriceValue
        )
        
        if let existingGold = editingGold {
            portfolioViewModel.updateGold(existingGold, with: newGold)
        } else {
            portfolioViewModel.addGold(newGold)
        }
        
        dismiss()
    }
}

#Preview {
    AddEditGoldView(portfolioViewModel: PortfolioViewModel(), editingGold: nil)
} 