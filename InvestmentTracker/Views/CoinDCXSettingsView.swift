import SwiftUI

struct CoinDCXSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    @ObservedObject private var coinDCXService = CoinDCXService.shared
    
    @State private var apiKey = ""
    @State private var apiSecret = ""
    @State private var isTestingConnection = false
    @State private var showingSuccessAlert = false
    @State private var showingErrorAlert = false
    @State private var errorMessage = ""
    @State private var showingDeleteConfirmation = false
    @State private var isSyncing = false
    
    private var hasExistingCredentials: Bool {
        KeychainManager.shared.hasCoinDCXCredentials()
    }
    
    private var canSave: Bool {
        !apiKey.isEmpty && !apiSecret.isEmpty && apiKey.count > 10 && apiSecret.count > 10
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("CoinDCX API Integration")
                            .font(.headline)
                        Text("Connect your CoinDCX account to automatically sync your crypto portfolio. Your API credentials are stored securely in the iOS Keychain.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 4)
                }
                
                Section("API Credentials") {
                    VStack(alignment: .leading, spacing: 12) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("API Key")
                                .font(.subheadline)
                                .fontWeight(.medium)
                            SecureField("Enter your CoinDCX API Key", text: $apiKey)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                        }
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("API Secret")
                                .font(.subheadline)
                                .fontWeight(.medium)
                            SecureField("Enter your CoinDCX API Secret", text: $apiSecret)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                        }
                    }
                    .padding(.vertical, 4)
                }
                
                Section("Actions") {
                    Button(action: testConnection) {
                        HStack {
                            if isTestingConnection {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "network")
                            }
                            Text("Test Connection")
                        }
                    }
                    .disabled(!canSave || isTestingConnection)
                    
                    Button(action: saveCredentials) {
                        HStack {
                            Image(systemName: "checkmark.circle")
                            Text(hasExistingCredentials ? "Update Credentials" : "Save Credentials")
                        }
                    }
                    .disabled(!canSave)
                    
                    if hasExistingCredentials {
                        Button(action: syncPortfolio) {
                            HStack {
                                if isSyncing {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: "arrow.clockwise")
                                }
                                Text("Sync Portfolio Now")
                            }
                        }
                        .disabled(isSyncing)
                        
                        Button(action: { showingDeleteConfirmation = true }) {
                            HStack {
                                Image(systemName: "trash")
                                Text("Remove Credentials")
                            }
                        }
                        .foregroundColor(.red)
                    }
                }
                
                if hasExistingCredentials {
                    Section("Status") {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("API credentials are configured")
                        }
                        
                        if let lastSyncTime = coinDCXService.lastSyncTime {
                            HStack {
                                Image(systemName: "clock")
                                Text("Last sync: \(lastSyncTime, style: .relative) ago")
                            }
                            .font(.caption)
                            .foregroundColor(.secondary)
                        }
                    }
                }
                
                Section("How to get API credentials") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("1. Go to CoinDCX Pro → Profile → API Dashboard")
                        Text("2. Click 'Create API Key'")
                        Text("3. Enter a label name")
                        Text("4. Complete email and SMS verification")
                        Text("5. Copy the API Key and Secret")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
            }
            .navigationTitle("CoinDCX Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
            .onAppear {
                loadExistingCredentials()
            }
            .alert("Success", isPresented: $showingSuccessAlert) {
                Button("OK") { }
            } message: {
                Text("API credentials saved successfully!")
            }
            .alert("Error", isPresented: $showingErrorAlert) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }
            .confirmationDialog("Remove API Credentials", isPresented: $showingDeleteConfirmation) {
                Button("Remove", role: .destructive) {
                    removeCredentials()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                Text("This will remove your CoinDCX API credentials from this device. You can add them again later.")
            }
        }
    }
    
    private func loadExistingCredentials() {
        if hasExistingCredentials {
            let credentials = KeychainManager.shared.getCoinDCXCredentials()
            // Show masked versions for security
            if let key = credentials.apiKey, key.count > 8 {
                apiKey = String(key.prefix(8)) + "..."
            }
            if let secret = credentials.apiSecret, secret.count > 8 {
                apiSecret = String(secret.prefix(8)) + "..."
            }
        }
    }
    
    private func testConnection() {
        isTestingConnection = true

        Task {
            // First run debug call to see raw response
            await CoinDCXService.shared.debugAPICall(
                apiKey: apiKey,
                apiSecret: apiSecret
            )

            // Then run normal test
            let isValid = await CoinDCXService.shared.testConnection(
                apiKey: apiKey,
                apiSecret: apiSecret
            )

            await MainActor.run {
                isTestingConnection = false

                if isValid {
                    showingSuccessAlert = true
                } else {
                    errorMessage = coinDCXService.errorMessage ?? "Connection test failed"
                    showingErrorAlert = true
                }
            }
        }
    }
    
    private func saveCredentials() {
        // Clear any previous errors
        coinDCXService.clearError()

        // Validate credentials format
        guard CoinDCXService.shared.validateCredentials(apiKey: apiKey, apiSecret: apiSecret) else {
            errorMessage = "Invalid credentials format. Please check your API key and secret."
            showingErrorAlert = true
            return
        }

        let success = KeychainManager.shared.saveCoinDCXCredentials(
            apiKey: apiKey,
            apiSecret: apiSecret
        )

        if success {
            showingSuccessAlert = true
        } else {
            errorMessage = "Failed to save credentials to Keychain"
            showingErrorAlert = true
        }
    }
    
    private func syncPortfolio() {
        isSyncing = true
        
        Task {
            let success = await portfolioViewModel.forceSyncCoinDCXPortfolio()
            
            await MainActor.run {
                isSyncing = false
                
                if success {
                    showingSuccessAlert = true
                } else {
                    errorMessage = "Failed to sync portfolio. Please check your credentials."
                    showingErrorAlert = true
                }
            }
        }
    }
    
    private func removeCredentials() {
        let success = KeychainManager.shared.deleteCoinDCXCredentials()
        
        if success {
            apiKey = ""
            apiSecret = ""
            showingSuccessAlert = true
        } else {
            errorMessage = "Failed to remove credentials"
            showingErrorAlert = true
        }
    }
}

#Preview {
    CoinDCXSettingsView(portfolioViewModel: PortfolioViewModel())
}
