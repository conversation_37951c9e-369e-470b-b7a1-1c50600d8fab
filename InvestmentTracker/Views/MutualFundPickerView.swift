import SwiftUI

struct MutualFundPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var mfNavService = MFNAVService()
    
    let onFundSelected: (MFSearchResult) -> Void
    
    @State private var searchText = ""
    @State private var selectedFunds: Set<UUID> = []
    @State private var searchResults: [MFSearchResult] = []
    @State private var popularFunds: [MFSearchResult] = []
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Search Bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search mutual funds...", text: $searchText)
                        .textFieldStyle(.plain)
                        .onChange(of: searchText) { _, newValue in
                            performSearch(newValue)
                        }
                    
                    if !searchText.isEmpty {
                        Button(action: { searchText = "" }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                
                if mfNavService.isLoading {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("Searching...")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
                
                ScrollView {
                    LazyVStack(spacing: 16) {
                        let fundsToShow = searchText.isEmpty ? popularFunds : searchResults
                        
                        if fundsToShow.isEmpty && !mfNavService.isLoading {
                            VStack(spacing: 12) {
                                Image(systemName: searchText.isEmpty ? "list.bullet" : "magnifyingglass")
                                    .font(.system(size: 40))
                                    .foregroundColor(.secondary)
                                
                                Text(searchText.isEmpty ? "Loading popular funds..." : "No funds found")
                                    .font(.headline)
                                    .foregroundColor(.secondary)
                                
                                Text(searchText.isEmpty ? "Please wait while we fetch the latest mutual funds" : "Try searching with different keywords")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.top, 50)
                        } else {
                            if searchText.isEmpty && !popularFunds.isEmpty {
                                VStack(alignment: .leading, spacing: 12) {
                                    HStack {
                                        Text("Popular Mutual Funds")
                                            .font(.headline)
                                            .fontWeight(.semibold)
                                            .foregroundColor(.primary)
                                        
                                        Spacer()
                                        
                                        Text("\(popularFunds.count) funds")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    .padding(.horizontal)
                                    
                                    LazyVGrid(columns: [GridItem(.flexible())], spacing: 8) {
                                        ForEach(popularFunds, id: \.id) { fund in
                                            MutualFundRow(fund: fund) {
                                                onFundSelected(fund)
                                            }
                                        }
                                    }
                                    .padding(.horizontal)
                                }
                            } else {
                                LazyVGrid(columns: [GridItem(.flexible())], spacing: 8) {
                                    ForEach(searchResults, id: \.id) { fund in
                                        MutualFundRow(fund: fund) {
                                            onFundSelected(fund)
                                        }
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                    }
                    .padding(.vertical)
                }
            }
            .navigationTitle("Select Mutual Fund")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                loadPopularFunds()
            }
        }
    }
    
    private func loadPopularFunds() {
        Task {
            let results = await mfNavService.searchMutualFunds(query: "")
            await MainActor.run {
                popularFunds = results
            }
        }
    }
    
    private func performSearch(_ query: String) {
        guard !query.isEmpty else {
            searchResults = []
            return
        }
        
        Task {
            let results = await mfNavService.searchMutualFunds(query: query)
            await MainActor.run {
                searchResults = results
            }
        }
    }
}

struct MutualFundRow: View {
    let fund: MFSearchResult
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(fund.schemeName)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)
                            .lineLimit(2)
                        
                        Text("Code: \(fund.schemeCode)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 1, x: 0, y: 1)
            )
        }
        .buttonStyle(.plain)
    }
}

#Preview {
    MutualFundPickerView { fund in
        print("Selected fund: \(fund.schemeName)")
    }
} 