import SwiftUI

struct CryptoView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    @ObservedObject private var cryptoService = CryptoPriceService.shared
    @State private var showingAddCrypto = false
    @State private var selectedCrypto: Crypto?
    @State private var showingEditCrypto = false
    @State private var showingCryptoPicker = false
    @State private var showingErrorAlert = false
    @State private var errorMessage = ""
    @State private var lastRefreshTime = Date()
    
    private var totalInvested: Double {
        portfolioViewModel.cryptos.reduce(0) { $0 + $1.investedAmount }
    }
    
    private var totalCurrentValue: Double {
        portfolioViewModel.cryptos.reduce(0) { $0 + $1.currentValue }
    }
    
    private var totalProfitLoss: Double {
        totalCurrentValue - totalInvested
    }
    
    private var totalProfitLossPercentage: Double {
        guard totalInvested > 0 else { return 0 }
        return (totalProfitLoss / totalInvested) * 100
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                if portfolioViewModel.cryptos.isEmpty {
                    // Empty state
                    VStack(spacing: 20) {
                        Image(systemName: "bitcoinsign.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.orange)
                            .padding()
                        
                        Text("No Crypto Yet")
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        Text("Track your cryptocurrency investments")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .padding(.bottom, 20)
                        
                        VStack(spacing: 12) {
                            Button("Add Crypto Manually") {
                                showingAddCrypto = true
                            }
                            .buttonStyle(.borderedProminent)
                            
                            Button("Choose from Popular Cryptos") {
                                showingCryptoPicker = true
                            }
                            .buttonStyle(.bordered)
                        }
                    }
                    .padding(.top, 50)
                } else {
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            // Portfolio Summary Card
                            VStack(alignment: .leading, spacing: 16) {
                                HStack {
                                    Text("Crypto Portfolio")
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                    
                                    Spacer()
                                    
                                    Text("\(portfolioViewModel.cryptos.count) holdings")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                HStack {
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text("Total Invested")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                        Text(formatCurrency(totalInvested))
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                    }
                                    
                                    Spacer()
                                    
                                    VStack(alignment: .center, spacing: 4) {
                                        Text("Current Value")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                        Text(formatCurrency(totalCurrentValue))
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                    }
                                    
                                    Spacer()
                                    
                                    VStack(alignment: .trailing, spacing: 4) {
                                        Text("P&L")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                        VStack(alignment: .trailing, spacing: 2) {
                                            Text(formatCurrency(totalProfitLoss))
                                                .font(.subheadline)
                                                .fontWeight(.semibold)
                                                .foregroundColor(totalProfitLoss >= 0 ? .green : .red)
                                            Text("(\(String(format: "%.1f", totalProfitLossPercentage))%)")
                                                .font(.caption)
                                                .foregroundColor(totalProfitLoss >= 0 ? .green : .red)
                                        }
                                    }
                                }
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(.systemGray6))
                            )
                            
                            // Crypto Holdings
                            ForEach(portfolioViewModel.cryptos, id: \.id) { crypto in
                                CryptoCard(
                                    crypto: crypto,
                                    onEdit: {
                                        print("🔧 Edit button pressed for \(crypto.name) (\(crypto.symbol))")
                                        selectedCrypto = crypto
                                        print("🔧 selectedCrypto set to: \(selectedCrypto?.name ?? "nil")")
                                        showingEditCrypto = true
                                        print("🔧 showingEditCrypto set to: \(showingEditCrypto)")
                                    },
                                    onRefreshPrice: {
                                        refreshCryptoPrice(crypto)
                                    },
                                    onDelete: {
                                        portfolioViewModel.deleteCrypto(crypto.id)
                                    }
                                )
                            }
                        }
                        .padding(.horizontal)
                        .padding(.vertical)
                    }
                }
            }
            .navigationTitle("Crypto")
            .toolbar {
                ToolbarItemGroup(placement: .navigationBarTrailing) {
                    if !portfolioViewModel.cryptos.isEmpty {
                        Button(action: refreshAllCryptoPrices) {
                            Image(systemName: "arrow.clockwise")
                        }
                        .disabled(cryptoService.isLoading)
                    }
                    
                    Menu {
                        Button("Add Manually") {
                            showingAddCrypto = true
                        }
                        
                        Button("Choose from Popular") {
                            showingCryptoPicker = true
                        }
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingAddCrypto) {
                AddEditCryptoView(portfolioViewModel: portfolioViewModel, crypto: nil)
            }
            .sheet(isPresented: $showingEditCrypto) {
                AddEditCryptoView(portfolioViewModel: portfolioViewModel, crypto: selectedCrypto)
            }
            .sheet(isPresented: $showingCryptoPicker) {
                CryptoPickerView { selectedCoin, currentPrice in
                    // Pre-fill the add crypto form with selected coin
                    let priceToUse = currentPrice ?? selectedCoin.currentPrice
                    let newCrypto = Crypto(
                        name: selectedCoin.name,
                        symbol: selectedCoin.symbol,
                        exchange: selectedCoin.exchange,
                        quantity: 0,
                        purchasePrice: priceToUse > 0 ? priceToUse : 0,
                        currentPrice: priceToUse > 0 ? priceToUse : 0
                    )
                    selectedCrypto = newCrypto
                    showingEditCrypto = true
                }
            }
            .onAppear {
                portfolioViewModel.loadAllData()
            }
            .alert("Price Fetch Failed", isPresented: $showingErrorAlert) {
                Button("OK") { 
                    print("Alert dismissed")
                }
            } message: {
                Text(errorMessage)
            }
        }
    }
    
    private func refreshCryptoPrice(_ crypto: Crypto) {
        Task {
            print("Refreshing price for \(crypto.name) (\(crypto.symbol))")
            let result = await cryptoService.fetchCurrentPriceWithDebug(for: crypto.symbol)
            
            if let newPrice = result.price {
                let updatedCrypto = crypto.updateCurrentPrice(newPrice)
                await MainActor.run {
                    portfolioViewModel.updateCrypto(crypto.id, with: updatedCrypto)
                }
                print("Successfully updated \(crypto.symbol) price to ₹\(newPrice)")
            } else {
                print("Failed to fetch current price for \(crypto.symbol)")
                await MainActor.run {
                    errorMessage = "Unable to fetch current price for \(crypto.symbol). Please try again later."
                    showingErrorAlert = true
                }
            }
        }
    }
    
    private func refreshAllCryptoPrices() {
        Task {
            print("Refreshing all crypto prices...")
            var successCount = 0
            var failureCount = 0
            
            for crypto in portfolioViewModel.cryptos {
                let result = await cryptoService.fetchCurrentPriceWithDebug(for: crypto.symbol)
                
                if let newPrice = result.price {
                    let updatedCrypto = crypto.updateCurrentPrice(newPrice)
                    await MainActor.run {
                        portfolioViewModel.updateCrypto(crypto.id, with: updatedCrypto)
                    }
                    successCount += 1
                    print("Successfully updated \(crypto.symbol) price to ₹\(newPrice)")
                } else {
                    failureCount += 1
                    print("Failed to fetch current price for \(crypto.symbol)")
                }
            }
            
            await MainActor.run {
                if failureCount == 0 {
                    // All successful - no alert needed, just update last refresh time
                    lastRefreshTime = Date()
                } else if successCount == 0 {
                    // All failed
                    errorMessage = "Unable to fetch current prices. Please check your internet connection and try again."
                    showingErrorAlert = true
                } else {
                    // Mixed results
                    errorMessage = "Updated \(successCount) crypto prices successfully. \(failureCount) failed to update."
                    showingErrorAlert = true
                }
                lastRefreshTime = Date()
            }
        }
    }
}

struct CryptoCard: View {
    let crypto: Crypto
    let onEdit: () -> Void
    let onRefreshPrice: () -> Void
    let onDelete: () -> Void
    
    @State private var showingDeleteAlert = false
    
    private var profitLoss: Double {
        crypto.currentValue - crypto.investedAmount
    }
    
    private var profitLossPercentage: Double {
        guard crypto.investedAmount > 0 else { return 0 }
        return (profitLoss / crypto.investedAmount) * 100
    }
    
    private var isProfit: Bool {
        profitLoss >= 0
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 8) {
                        Text(crypto.symbol)
                            .font(.headline)
                            .fontWeight(.bold)
                        
                        Text(crypto.name)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                    
                    HStack(spacing: 8) {
                        Text(crypto.exchange)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color(.systemGray6))
                            .cornerRadius(4)
                        
                        if let lastUpdated = formatRelativeDate(crypto.lastUpdated) {
                            Text(lastUpdated)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(formatCurrency(crypto.currentValue))
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 4) {
                        Image(systemName: isProfit ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        
                        Text("\(isProfit ? "+" : "")\(formatCurrency(profitLoss))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("(\(String(format: "%.1f", profitLossPercentage))%)")
                            .font(.caption)
                    }
                    .foregroundColor(isProfit ? .green : .red)
                }
            }
            
            Divider()
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Quantity")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(String(format: "%.6f", crypto.quantity))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .center, spacing: 2) {
                    Text("Avg Price")
                        .font(.caption)
                        .foregroundColor(.secondary)
                                            Text(formatCryptoPrice(crypto.purchasePrice))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    HStack(spacing: 4) {
                        Text("Current Price")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Button(action: onRefreshPrice) {
                            Image(systemName: "arrow.clockwise")
                                .font(.caption)
                        }
                        .buttonStyle(.plain)
                    }
                    
                                            Text(formatCryptoPrice(crypto.currentPrice))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
            
            // Action Buttons
            HStack {
                Button("Edit") {
                    onEdit()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                
                Spacer()
                
                Button("Delete") {
                    showingDeleteAlert = true
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                .foregroundColor(.red)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .alert("Delete Crypto", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                onDelete()
            }
        } message: {
            Text("Are you sure you want to delete \(crypto.name) (\(crypto.symbol))? This action cannot be undone.")
        }
    }
}

// Helper function for relative date formatting
private func formatRelativeDate(_ date: Date) -> String? {
    let formatter = RelativeDateTimeFormatter()
    formatter.dateTimeStyle = .named
    return formatter.localizedString(for: date, relativeTo: Date())
}

#Preview {
    CryptoView(portfolioViewModel: PortfolioViewModel())
} 