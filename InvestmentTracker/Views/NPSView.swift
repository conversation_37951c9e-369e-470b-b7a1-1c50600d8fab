import SwiftUI

struct NPSView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    @State private var showingAddSheet = false
    @State private var selectedNPS: NPS?
    @State private var showingEditSheet = false
    @State private var showingDeleteAlert = false
    @State private var npsToDelete: NPS?
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    if portfolioViewModel.nps.isEmpty {
                        // Empty state
                        VStack(spacing: 20) {
                            Image(systemName: "shield.fill")
                                .font(.system(size: 60))
                                .foregroundColor(.orange)
                                .padding()
                            
                            Text("No NPS Records Yet")
                                .font(.title2)
                                .fontWeight(.semibold)
                            
                            Text("Add your NPS balance to start tracking")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .padding(.bottom, 20)
                            
                            But<PERSON>("Add NPS Entry") {
                                showingAddSheet = true
                            }
                            .buttonStyle(.borderedProminent)
                        }
                        .padding(.top, 50)
                    } else {
                        // NPS Summary Card
                        NPSSummaryCard(npsEntries: portfolioViewModel.nps)
                        
                        // Individual NPS Cards
                        ForEach(portfolioViewModel.nps, id: \.id) { nps in
                            EnhancedNPSCard(
                                nps: nps,
                                onEdit: {
                                    selectedNPS = nps
                                    showingEditSheet = true
                                },
                                onDelete: {
                                    npsToDelete = nps
                                    showingDeleteAlert = true
                                }
                            )
                        }
                    }
                }
                .padding(.horizontal)
            }
            .navigationTitle("NPS")
            .toolbar {
                if portfolioViewModel.nps.isEmpty {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Add") {
                            showingAddSheet = true
                        }
                        .foregroundColor(.orange)
                    }
                }
            }
            .sheet(isPresented: $showingAddSheet) {
                AddEditNPSView(
                    portfolioViewModel: portfolioViewModel,
                    editingNPS: nil
                )
            }
            .sheet(isPresented: $showingEditSheet) {
                AddEditNPSView(
                    portfolioViewModel: portfolioViewModel,
                    editingNPS: selectedNPS
                )
            }
            .alert("Delete NPS Entry", isPresented: $showingDeleteAlert) {
                Button("Delete", role: .destructive) {
                    if let nps = npsToDelete {
                        portfolioViewModel.deleteNPS(nps)
                    }
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                if npsToDelete != nil {
                    Text("Are you sure you want to delete this NPS entry?")
                }
            }
            .onAppear {
                portfolioViewModel.loadAllData()
            }
        }
    }
}

// MARK: - NPS Summary Card
struct NPSSummaryCard: View {
    let npsEntries: [NPS]
    
    private var totalInvested: Double {
        npsEntries.reduce(0) { $0 + $1.investedAmount }
    }
    
    private var totalCurrentValue: Double {
        npsEntries.reduce(0) { $0 + $1.currentValue }
    }
    
    private var totalProfitLoss: Double {
        totalCurrentValue - totalInvested
    }
    
    private var profitLossPercentage: Double {
        guard totalInvested > 0 else { return 0 }
        return (totalProfitLoss / totalInvested) * 100
    }
    
    var body: some View {
        VStack(spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("NPS Account")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("National Pension System")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(CurrencyFormatter.shared.format(totalCurrentValue))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Current Value")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Divider()
            
            // Statistics Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                StatCard(
                    title: "Total Invested",
                    value: CurrencyFormatter.shared.format(totalInvested),
                    icon: "arrow.down.circle.fill",
                    color: .blue
                )
                
                StatCard(
                    title: "Profit/Loss",
                    value: CurrencyFormatter.shared.format(totalProfitLoss),
                    icon: totalProfitLoss >= 0 ? "arrow.up.circle.fill" : "arrow.down.circle.fill",
                    color: totalProfitLoss >= 0 ? .green : .red
                )
            }
            
            // P&L Percentage
            HStack {
                Text("Returns")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(profitLossPercentage >= 0 ? "+" : "")\(String(format: "%.2f", profitLossPercentage))%")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(profitLossPercentage >= 0 ? .green : .red)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Enhanced NPS Card
struct EnhancedNPSCard: View {
    let nps: NPS
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    private var profitLoss: Double {
        nps.currentValue - nps.investedAmount
    }
    
    private var profitLossPercentage: Double {
        guard nps.investedAmount > 0 else { return 0 }
        return (profitLoss / nps.investedAmount) * 100
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with NPS name and actions
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("National Pension System")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("Last updated \(DateHelper.shared.format(nps.lastUpdated, style: .short))")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Menu {
                    Button("Edit", action: onEdit)
                    Button("Delete", role: .destructive, action: onDelete)
                } label: {
                    Image(systemName: "ellipsis.circle")
                        .font(.title2)
                        .foregroundColor(.secondary)
                }
            }
            
            // Current Value
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Current Balance")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(CurrencyFormatter.shared.format(nps.currentValue))
                        .font(.title2)
                        .fontWeight(.bold)
                }
            }
            
            Divider()
            
            // Details Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                DetailItem(
                    title: "Total Invested",
                    value: CurrencyFormatter.shared.format(nps.investedAmount)
                )
                
                DetailItem(
                    title: "Profit/Loss",
                    value: CurrencyFormatter.shared.format(profitLoss)
                )
                
                DetailItem(
                    title: "Returns",
                    value: "\(profitLossPercentage >= 0 ? "+" : "")\(String(format: "%.2f", profitLossPercentage))%"
                )
                
                DetailItem(
                    title: "Updated On",
                    value: DateHelper.shared.format(nps.lastUpdated, style: .short)
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - Add/Edit NPS View
struct AddEditNPSView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    let editingNPS: NPS?
    
    @Environment(\.dismiss) private var dismiss
    
    @State private var investedAmount: String
    @State private var currentValue: String
    @State private var lastUpdated: Date
    
    @State private var showingError = false
    @State private var errorMessage = ""
    
    init(portfolioViewModel: PortfolioViewModel, editingNPS: NPS?) {
        self.portfolioViewModel = portfolioViewModel
        self.editingNPS = editingNPS
        
        if let nps = editingNPS {
            self._investedAmount = State(initialValue: String(nps.investedAmount))
            self._currentValue = State(initialValue: String(nps.currentValue))
            self._lastUpdated = State(initialValue: nps.investmentDate)
        } else {
            self._investedAmount = State(initialValue: "")
            self._currentValue = State(initialValue: "")
            self._lastUpdated = State(initialValue: Date())
        }
    }
    
    private var isEditing: Bool {
        editingNPS != nil
    }
    
    private var isFormValid: Bool {
        !investedAmount.isEmpty &&
        !currentValue.isEmpty &&
        Double(investedAmount) != nil &&
        Double(currentValue) != nil
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("NPS Details") {
                    HStack {
                        Text("₹")
                            .foregroundColor(.secondary)
                        TextField("Total Invested Amount", text: $investedAmount)
                            .keyboardType(.decimalPad)
                    }
                    
                    HStack {
                        Text("₹")
                            .foregroundColor(.secondary)
                        TextField("Current Balance", text: $currentValue)
                            .keyboardType(.decimalPad)
                    }
                    
                    DatePicker("Last Updated", selection: $lastUpdated, displayedComponents: .date)
                }
                
                if let invested = Double(investedAmount),
                   let current = Double(currentValue),
                   invested > 0 {
                    Section("Calculated Values") {
                        let profitLoss = current - invested
                        let percentage = (profitLoss / invested) * 100
                        
                        HStack {
                            Text("Profit/Loss")
                            Spacer()
                            Text(CurrencyFormatter.shared.format(profitLoss))
                                .foregroundColor(profitLoss >= 0 ? .green : .red)
                                .fontWeight(.semibold)
                        }
                        
                        HStack {
                            Text("Returns")
                            Spacer()
                            Text("\(percentage >= 0 ? "+" : "")\(String(format: "%.2f", percentage))%")
                                .foregroundColor(percentage >= 0 ? .green : .red)
                                .fontWeight(.semibold)
                        }
                    }
                }
            }
            .navigationTitle(isEditing ? "Edit NPS" : "Add NPS")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveNPS()
                    }
                    .disabled(!isFormValid)
                }
            }
            .alert("Error", isPresented: $showingError) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }

        }
    }
    

    
    private func saveNPS() {
        guard let invested = Double(investedAmount),
              let current = Double(currentValue) else {
            showError("Please enter valid amounts")
            return
        }
        
        guard invested > 0 && current > 0 else {
            showError("Amounts must be greater than 0")
            return
        }
        
        let nps = NPS(
            investedAmount: invested,
            currentValue: current,
            investmentDate: lastUpdated
        )
        
        if let editingNPS = editingNPS {
            portfolioViewModel.updateNPS(editingNPS, with: nps)
        } else {
            portfolioViewModel.addNPS(nps)
        }
        
        dismiss()
    }
    
    private func showError(_ message: String) {
        errorMessage = message
        showingError = true
    }
}

#Preview {
    NPSView(portfolioViewModel: PortfolioViewModel())
} 