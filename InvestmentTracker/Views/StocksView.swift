import SwiftUI

struct StocksView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    @State private var showingAddStock = false
    @State private var showingEditStock = false
    @State private var selectedStock: Stock?
    @State private var showingStockPicker = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    if portfolioViewModel.stocks.isEmpty {
                        // Empty state
                        VStack(spacing: 20) {
                            Image(systemName: "chart.line.uptrend.xyaxis")
                                .font(.system(size: 60))
                                .foregroundColor(.blue)
                                .padding()
                            
                            Text("No Stocks Yet")
                                .font(.title2)
                                .fontWeight(.semibold)
                            
                            Text("Track your stock investments with live prices")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .padding(.bottom, 20)
                            
                            VStack(spacing: 12) {
                                But<PERSON>("Add Stock Manually") {
                                    showingAddStock = true
                                }
                                .buttonStyle(.borderedProminent)
                                
                                But<PERSON>("Choose from Popular Stocks") {
                                    showingStockPicker = true
                                }
                                .buttonStyle(.bordered)
                            }
                        }
                        .padding(.top, 50)
                    } else {
                        // Summary header
                        StocksSummaryCard(stocks: portfolioViewModel.stocks)
                        
                        // Stocks list
                        ForEach(portfolioViewModel.stocks, id: \.id) { stock in
                            StockDetailCard(
                                stock: stock,
                                onEdit: {
                                    selectedStock = stock
                                    showingEditStock = true
                                },
                                onDelete: {
                                    portfolioViewModel.deleteStock(stock)
                                },
                                onRefreshPrice: {
                                    portfolioViewModel.refreshSingleStockPrice(stock)
                                }
                            )
                        }
                    }
                }
                .padding(.horizontal)
            }
            .navigationTitle("Stocks")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack {
                        if !portfolioViewModel.stocks.isEmpty {
                            Button(action: {
                                portfolioViewModel.refreshStockPrices()
                            }) {
                                Image(systemName: "arrow.clockwise")
                            }
                            .disabled(StockPriceService.shared.isLoading)
                        }
                        
                        Menu {
                            Button("Add Stock Manually") {
                                showingAddStock = true
                            }
                            
                            Button("Choose from Popular Stocks") {
                                showingStockPicker = true
                            }
                        } label: {
                            Image(systemName: "plus")
                        }
                    }
                }
            }
            .onAppear {
                portfolioViewModel.loadAllData()
            }
        }
        .sheet(isPresented: $showingAddStock) {
            AddEditStockView(
                portfolioViewModel: portfolioViewModel,
                stock: nil
            )
        }
        .sheet(isPresented: $showingEditStock) {
            AddEditStockView(
                portfolioViewModel: portfolioViewModel,
                stock: selectedStock
            )
        }
        .sheet(isPresented: $showingStockPicker) {
            StockPickerView(portfolioViewModel: portfolioViewModel)
        }
    }
}

// MARK: - Summary Card
struct StocksSummaryCard: View {
    let stocks: [Stock]
    
    private var totalInvested: Double {
        stocks.reduce(0) { $0 + $1.investedAmount }
    }
    
    private var totalCurrent: Double {
        stocks.reduce(0) { $0 + $1.currentValue }
    }
    
    private var totalProfitLoss: Double {
        totalCurrent - totalInvested
    }
    
    private var totalProfitLossPercentage: Double {
        guard totalInvested > 0 else { return 0 }
        return (totalProfitLoss / totalInvested) * 100
    }
    
    private var isProfit: Bool {
        totalProfitLoss >= 0
    }
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Stocks Portfolio")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(stocks.count) holdings")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Total Invested")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(formatCurrency(totalInvested))
                            .font(.title2)
                            .fontWeight(.semibold)
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Current Value")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(formatCurrency(totalCurrent))
                            .font(.title2)
                            .fontWeight(.semibold)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 8) {
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("Total P&L")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        HStack(spacing: 4) {
                            Image(systemName: isProfit ? "arrow.up.right" : "arrow.down.right")
                                .font(.caption)
                            
                            Text("\(isProfit ? "+" : "")\(formatCurrency(totalProfitLoss))")
                                .font(.title2)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(isProfit ? .green : .red)
                    }
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("Returns")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("\(isProfit ? "+" : "")\(String(format: "%.2f", totalProfitLossPercentage))%")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(isProfit ? .green : .red)
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - Stock Detail Card
struct StockDetailCard: View {
    let stock: Stock
    let onEdit: () -> Void
    let onDelete: () -> Void
    let onRefreshPrice: () -> Void
    
    @State private var showingDeleteAlert = false
    
    private var profitLoss: Double {
        stock.currentValue - stock.investedAmount
    }
    
    private var profitLossPercentage: Double {
        guard stock.investedAmount > 0 else { return 0 }
        return (profitLoss / stock.investedAmount) * 100
    }
    
    private var isProfit: Bool {
        profitLoss >= 0
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(stock.symbol)
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Text("(\(stock.exchange))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color(.systemGray5))
                            .cornerRadius(4)
                    }
                    
                    Text(stock.name)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(formatCurrency(stock.currentValue))
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 4) {
                        Image(systemName: isProfit ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        
                        Text("\(isProfit ? "+" : "")\(formatCurrency(profitLoss))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("(\(String(format: "%.1f", profitLossPercentage))%)")
                            .font(.caption)
                    }
                    .foregroundColor(isProfit ? .green : .red)
                }
            }
            
            Divider()
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Quantity")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(String(format: "%.0f", stock.quantity))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .center, spacing: 2) {
                    Text("Avg Price")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(formatCurrency(stock.purchasePrice))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    HStack(spacing: 4) {
                        Text("Current Price")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Button(action: onRefreshPrice) {
                            Image(systemName: "arrow.clockwise")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                        .disabled(StockPriceService.shared.isLoading)
                    }
                    
                    Text(formatCurrency(stock.currentPrice))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
            
            HStack {
                Text("Last updated: \(formatDate(stock.lastUpdated))")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                HStack(spacing: 12) {
                    Button("Edit") {
                        onEdit()
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                    
                    Button("Delete") {
                        showingDeleteAlert = true
                    }
                    .font(.caption)
                    .foregroundColor(.red)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .alert("Delete Stock", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                onDelete()
            }
        } message: {
            Text("Are you sure you want to delete \(stock.symbol)? This action cannot be undone.")
        }
    }
}

#Preview {
    StocksView(portfolioViewModel: PortfolioViewModel())
} 