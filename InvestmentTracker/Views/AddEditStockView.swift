import SwiftUI

struct AddEditStockView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    @ObservedObject private var stockPriceService = StockPriceService.shared
    
    let stock: Stock? // nil for add, populated for edit
    
    @State private var name = ""
    @State private var symbol = ""
    @State private var exchange = "NSE"
    @State private var quantity = ""
    @State private var purchasePrice = ""
    @State private var currentPrice = ""
    @State private var investmentDate = Date()
    
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    private let exchanges = ["NSE", "BSE"]
    
    private var isEditing: Bool {
        stock != nil
    }
    
    private var isFormValid: Bool {
        !name.isEmpty && 
        !symbol.isEmpty && 
        !quantity.isEmpty && 
        !purchasePrice.isEmpty && 
        !currentPrice.isEmpty &&
        Double(quantity) != nil &&
        Double(purchasePrice) != nil &&
        Double(currentPrice) != nil
    }
    
    var body: some View {
        NavigationStack {
            Form {
                VStack(alignment: .leading, spacing: 16) {
                    // Stock Details
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Stock Details")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        TextField("Company Name", text: $name)
                            .textInputAutocapitalization(.words)
                            .textFieldStyle(.roundedBorder)
                        
                        HStack {
                            TextField("Stock Symbol", text: $symbol)
                                .textInputAutocapitalization(.characters)
                                .textFieldStyle(.roundedBorder)
                                .onChange(of: symbol) { _, newValue in
                                    symbol = newValue.uppercased()
                                }
                            
                            Button(action: fetchCurrentPrice) {
                                Image(systemName: "magnifyingglass")
                                    .foregroundColor(.blue)
                            }
                            .disabled(symbol.isEmpty || stockPriceService.isLoading)
                        }
                        
                        Picker("Exchange", selection: $exchange) {
                            ForEach(exchanges, id: \.self) { exchange in
                                Text(exchange).tag(exchange)
                            }
                        }
                        .pickerStyle(.segmented)
                    }
                    
                    // Investment Details
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Investment Details")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        TextField("Quantity", text: $quantity)
                            .keyboardType(.decimalPad)
                            .textFieldStyle(.roundedBorder)
                        
                        TextField("Purchase Price per Share", text: $purchasePrice)
                            .keyboardType(.decimalPad)
                            .textFieldStyle(.roundedBorder)
                        
                        HStack {
                            TextField("Current Price per Share", text: $currentPrice)
                                .keyboardType(.decimalPad)
                                .textFieldStyle(.roundedBorder)
                            
                            Button(action: fetchCurrentPrice) {
                                Image(systemName: "arrow.clockwise")
                                    .foregroundColor(.blue)
                            }
                            .disabled(symbol.isEmpty || stockPriceService.isLoading)
                        }
                        
                        DatePicker("Investment Date", selection: $investmentDate, displayedComponents: .date)
                    }
                    
                    // Loading indicator
                    if stockPriceService.isLoading {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("Fetching live price...")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // Error message
                    if let errorMessage = stockPriceService.errorMessage {
                        Text(errorMessage)
                            .font(.subheadline)
                            .foregroundColor(.red)
                    }
                    
                    // Investment Summary
                    if isFormValid {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Investment Summary")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            if let qty = Double(quantity),
                               let purchaseP = Double(purchasePrice),
                               let currentP = Double(currentPrice) {
                                
                                let invested = qty * purchaseP
                                let current = qty * currentP
                                let pnl = current - invested
                                let pnlPercentage = invested > 0 ? (pnl / invested) * 100 : 0
                                let isProfit = pnl >= 0
                                
                                VStack(spacing: 4) {
                                    HStack {
                                        Text("Total Invested")
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        Spacer()
                                        
                                        Text(formatCurrency(invested))
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                    }
                                    
                                    HStack {
                                        Text("Current Value")
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        Spacer()
                                        
                                        Text(formatCurrency(current))
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                    }
                                    
                                    HStack {
                                        Text("P&L")
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        Spacer()
                                        
                                        VStack(alignment: .trailing, spacing: 2) {
                                            Text("\(isProfit ? "+" : "")\(formatCurrency(pnl))")
                                                .font(.subheadline)
                                                .fontWeight(.medium)
                                                .foregroundColor(isProfit ? .green : .red)
                                            
                                            Text("(\(isProfit ? "+" : "")\(String(format: "%.2f", pnlPercentage))%)")
                                                .font(.caption)
                                                .foregroundColor(isProfit ? .green : .red)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                .padding()
            }
            .navigationTitle(isEditing ? "Edit Stock" : "Add Stock")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(isEditing ? "Update" : "Add") {
                        saveStock()
                    }
                    .disabled(!isFormValid)
                }
            }
            .onAppear {
                loadStockData()
            }
        }
        .alert("Error", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private func loadStockData() {
        if let stock = stock {
            name = stock.name
            symbol = stock.symbol
            exchange = stock.exchange
            quantity = String(format: "%.0f", stock.quantity)
            purchasePrice = String(format: "%.2f", stock.purchasePrice)
            currentPrice = String(format: "%.2f", stock.currentPrice)
            investmentDate = stock.investmentDate
        }
    }
    
    private func fetchCurrentPrice() {
        guard !symbol.isEmpty else { return }
        
        Task {
            if let price = await stockPriceService.fetchStockPrice(symbol: symbol, exchange: exchange) {
                await MainActor.run {
                    currentPrice = String(format: "%.2f", price)
                }
            }
        }
    }
    
    private func saveStock() {
        guard let qty = Double(quantity),
              let purchaseP = Double(purchasePrice),
              let currentP = Double(currentPrice) else {
            alertMessage = "Please enter valid numeric values for quantity and prices."
            showingAlert = true
            return
        }
        
        if isEditing {
            // Update existing stock
            if stock != nil {
                let updatedStock = Stock(
                    name: name,
                    symbol: symbol,
                    exchange: exchange,
                    quantity: qty,
                    purchasePrice: purchaseP,
                    currentPrice: currentP,
                    investmentDate: investmentDate
                )
                
                portfolioViewModel.updateStock(updatedStock)
            }
        } else {
            // Add new stock
            let newStock = Stock(
                name: name,
                symbol: symbol,
                exchange: exchange,
                quantity: qty,
                purchasePrice: purchaseP,
                currentPrice: currentP,
                investmentDate: investmentDate
            )
            
            portfolioViewModel.addStock(newStock)
        }
        
        dismiss()
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "INR"
        formatter.currencySymbol = "₹"
        return formatter.string(from: NSNumber(value: amount)) ?? "₹0"
    }
}

#Preview {
    AddEditStockView(
        portfolioViewModel: PortfolioViewModel(),
        stock: nil
    )
} 