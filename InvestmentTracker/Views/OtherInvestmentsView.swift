import SwiftUI

struct OtherInvestmentsView: View {
    @ObservedObject var portfolioViewModel: PortfolioViewModel
    // @ObservedObject private var goldPriceService = GoldPriceService.shared
    
    @State private var showingAddGoldSheet = false
    @State private var showingEditGoldSheet = false
    @State private var selectedGold: Gold?
    @State private var showingDeleteGoldAlert = false
    @State private var goldToDelete: Gold?
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // Gold Section
                    if !portfolioViewModel.goldInvestments.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("Gold")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                
                                Spacer()
                                
                                Button(action: refreshGoldPrices) {
                                    Image(systemName: "arrow.clockwise")
                                        .font(.caption)
                                        .foregroundColor(.blue)
                                }
                                // .disabled(goldPriceService.isLoading)
                            }
                            .padding(.horizontal)
                            
                            ForEach(portfolioViewModel.goldInvestments, id: \.id) { gold in
                                EnhancedGoldCard(
                                    gold: gold,
                                    onEdit: {
                                        selectedGold = gold
                                        showingEditGoldSheet = true
                                    },
                                    onDelete: {
                                        goldToDelete = gold
                                        showingDeleteGoldAlert = true
                                    }
                                )
                                .padding(.horizontal)
                            }
                        }
                    }
                    
                    // NPS Section
                    if !portfolioViewModel.nps.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("National Pension System")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .padding(.horizontal)
                            
                            ForEach(portfolioViewModel.nps, id: \.id) { nps in
                                NPSCard(nps: nps)
                                    .padding(.horizontal)
                            }
                        }
                    }
                    
                    // Empty state
                    if portfolioViewModel.nps.isEmpty && portfolioViewModel.goldInvestments.isEmpty {
                        VStack(spacing: 20) {
                            Image(systemName: "tray")
                                .font(.system(size: 60))
                                .foregroundColor(.gray)
                                .padding()
                            
                            Text("No Other Investments")
                                .font(.title2)
                                .fontWeight(.semibold)
                            
                            Text("Gold, NPS and other investments will appear here")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)
                            
                            Button("Add Gold Investment") {
                                showingAddGoldSheet = true
                            }
                            .buttonStyle(.borderedProminent)
                        }
                        .padding(.top, 50)
                    }
                }
                .padding(.vertical)
            }
            .navigationTitle("Other Investments")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Add Gold", action: {
                            showingAddGoldSheet = true
                        })
                        
                        Button("Add NPS", action: {
                            // TODO: Add NPS functionality
                        })
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingAddGoldSheet) {
                AddEditGoldView(portfolioViewModel: portfolioViewModel, editingGold: nil)
            }
            .sheet(isPresented: $showingEditGoldSheet) {
                AddEditGoldView(portfolioViewModel: portfolioViewModel, editingGold: selectedGold)
            }
            .alert("Delete Gold Investment", isPresented: $showingDeleteGoldAlert) {
                Button("Delete", role: .destructive) {
                    if let gold = goldToDelete {
                        portfolioViewModel.deleteGold(gold)
                    }
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                if let gold = goldToDelete {
                    Text("Are you sure you want to delete this gold investment of \(String(format: "%.2f", gold.grams)) grams?")
                }
            }
            .onAppear {
                portfolioViewModel.loadAllData()
            }
        }
    }
    
    private func refreshGoldPrices() {
        // TODO: Re-enable when GoldPriceService compilation is fixed
        /*
        Task {
            if let newPrice = await goldPriceService.fetchCurrentGoldPrice() {
                // Update all gold investments with the new current rate
                for index in portfolioViewModel.goldInvestments.indices {
                    let originalGold = portfolioViewModel.goldInvestments[index]
                    let updatedGold = Gold(
                        grams: originalGold.grams,
                        purchaseRate: originalGold.purchaseRate,
                        currentRate: newPrice,
                        investmentDate: originalGold.investmentDate
                    )
                    var namedGold = updatedGold
                    namedGold.name = originalGold.name
                    portfolioViewModel.updateGold(originalGold, with: namedGold)
                }
            }
        }
        */
    }
}

// MARK: - Investment Cards

struct FixedDepositCard: View {
    let fixedDeposit: FixedDeposit
    
    private var profitLoss: Double {
        fixedDeposit.maturityAmount - fixedDeposit.investedAmount
    }
    
    private var isMatured: Bool {
        fixedDeposit.maturityDate <= Date()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(fixedDeposit.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("\(String(format: "%.1f", fixedDeposit.interestRate))% p.a.")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(CurrencyFormatter.shared.format(fixedDeposit.maturityAmount))
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(isMatured ? "Matured" : "Active")
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(isMatured ? Color.green : Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(4)
                }
            }
            
            Divider()
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Invested")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(CurrencyFormatter.shared.format(fixedDeposit.investedAmount))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .center, spacing: 2) {
                    Text("Maturity Date")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(DateHelper.shared.format(fixedDeposit.maturityDate, style: .short))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("Interest")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(CurrencyFormatter.shared.format(profitLoss))
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.green)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

struct EPFCard: View {
    let epf: EPF
    
    private var profitLoss: Double {
        epf.currentValue - epf.investedAmount
    }
    
    private var profitLossPercentage: Double {
        guard epf.investedAmount > 0 else { return 0 }
        return (profitLoss / epf.investedAmount) * 100
    }
    
    private var isProfit: Bool {
        profitLoss >= 0
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Employee Provident Fund")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("Government Scheme")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(CurrencyFormatter.shared.format(epf.currentValue))
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 4) {
                        Image(systemName: isProfit ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        
                        Text("\(isProfit ? "+" : "")\(CurrencyFormatter.shared.format(profitLoss))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("(\(String(format: "%.1f", profitLossPercentage))%)")
                            .font(.caption)
                    }
                    .foregroundColor(isProfit ? .green : .red)
                }
            }
            
            Divider()
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Invested")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(CurrencyFormatter.shared.format(epf.investedAmount))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("Start Date")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(DateHelper.shared.format(epf.investmentDate, style: .short))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

struct NPSCard: View {
    let nps: NPS
    
    private var profitLoss: Double {
        nps.currentValue - nps.investedAmount
    }
    
    private var profitLossPercentage: Double {
        guard nps.investedAmount > 0 else { return 0 }
        return (profitLoss / nps.investedAmount) * 100
    }
    
    private var isProfit: Bool {
        profitLoss >= 0
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("National Pension System")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("Retirement Scheme")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(CurrencyFormatter.shared.format(nps.currentValue))
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 4) {
                        Image(systemName: isProfit ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        
                        Text("\(isProfit ? "+" : "")\(CurrencyFormatter.shared.format(profitLoss))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("(\(String(format: "%.1f", profitLossPercentage))%)")
                            .font(.caption)
                    }
                    .foregroundColor(isProfit ? .green : .red)
                }
            }
            
            Divider()
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Invested")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(CurrencyFormatter.shared.format(nps.investedAmount))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("Start Date")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(DateHelper.shared.format(nps.investmentDate, style: .short))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - Enhanced Gold Card with Actions
struct EnhancedGoldCard: View {
    let gold: Gold
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    private var profitLoss: Double {
        gold.currentValue - gold.investedAmount
    }
    
    private var profitLossPercentage: Double {
        guard gold.investedAmount > 0 else { return 0 }
        return (profitLoss / gold.investedAmount) * 100
    }
    
    private var isProfit: Bool {
        profitLoss >= 0
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with name and actions
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(gold.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("Precious Metal • \(String(format: "%.2f", gold.grams))g")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Action buttons
                HStack(spacing: 8) {
                    Button(action: onEdit) {
                        Image(systemName: "pencil")
                            .font(.caption)
                            .foregroundColor(.blue)
                            .frame(width: 28, height: 28)
                            .background(Color.blue.opacity(0.1))
                            .clipShape(Circle())
                    }
                    
                    Button(action: onDelete) {
                        Image(systemName: "trash")
                            .font(.caption)
                            .foregroundColor(.red)
                            .frame(width: 28, height: 28)
                            .background(Color.red.opacity(0.1))
                            .clipShape(Circle())
                    }
                }
            }
            
            // Current value and P&L
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Current Value")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(CurrencyFormatter.shared.format(gold.currentValue))
                        .font(.title3)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    HStack(spacing: 4) {
                        Image(systemName: isProfit ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        
                        Text("\(isProfit ? "+" : "")\(CurrencyFormatter.shared.format(profitLoss))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(isProfit ? .green : .red)
                    
                    Text("(\(String(format: "%.1f", profitLossPercentage))%)")
                        .font(.caption)
                        .foregroundColor(isProfit ? .green : .red)
                }
            }
            
            Divider()
            
            // Investment details
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Invested")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(CurrencyFormatter.shared.format(gold.investedAmount))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .center, spacing: 2) {
                    Text("Avg Rate")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("₹\(String(format: "%.0f", gold.purchaseRate))")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("Current Rate")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("₹\(String(format: "%.0f", gold.currentRate))")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
            
            // Investment date
            HStack {
                Text("Investment Date:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text(DateHelper.shared.format(gold.investmentDate))
                    .font(.caption)
                    .fontWeight(.medium)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

struct GoldCard: View {
    let gold: Gold
    
    private var profitLoss: Double {
        gold.currentValue - gold.investedAmount
    }
    
    private var profitLossPercentage: Double {
        guard gold.investedAmount > 0 else { return 0 }
        return (profitLoss / gold.investedAmount) * 100
    }
    
    private var isProfit: Bool {
        profitLoss >= 0
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(gold.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("Precious Metal")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(CurrencyFormatter.shared.format(gold.currentValue))
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 4) {
                        Image(systemName: isProfit ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        
                        Text("\(isProfit ? "+" : "")\(CurrencyFormatter.shared.format(profitLoss))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("(\(String(format: "%.1f", profitLossPercentage))%)")
                            .font(.caption)
                    }
                    .foregroundColor(isProfit ? .green : .red)
                }
            }
            
            Divider()
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Quantity")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(String(format: "%.2f", gold.grams))g")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .center, spacing: 2) {
                    Text("Avg Price")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(CurrencyFormatter.shared.format(gold.purchaseRate))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("Current Price")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(CurrencyFormatter.shared.format(gold.currentRate))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - Helper Functions

#Preview {
    OtherInvestmentsView(portfolioViewModel: PortfolioViewModel())
} 