import Foundation

// MARK: - Base Investment Protocol
protocol Investment: Identifiable, Codable {
    var id: UUID { get }
    var name: String { get set }
    var investedAmount: Double { get }
    var currentValue: Double { get }
    var investmentDate: Date { get set }
    var profitLoss: Double { get }
    var profitLossPercentage: Double { get }
}

extension Investment {
    var profitLoss: Double {
        return currentValue - investedAmount
    }
    
    var profitLossPercentage: Double {
        guard investedAmount > 0 else { return 0 }
        return (profitLoss / investedAmount) * 100
    }
}

// MARK: - Stock Model
struct Stock: Investment {
    let id = UUID()
    var name: String
    var symbol: String
    var exchange: String // NSE, BSE
    var quantity: Double
    var purchasePrice: Double
    var currentPrice: Double
    var investmentDate: Date
    var lastUpdated: Date
    
    // Private stored properties for Investment protocol compliance
    private var _investedAmount: Double
    private var _currentValue: Double
    
    // Investment protocol properties
    var investedAmount: Double {
        return _investedAmount
    }
    
    var currentValue: Double {
        return _currentValue
    }
    
    // Convenience initializer
    init(name: String, symbol: String, exchange: String = "NSE", quantity: Double, purchasePrice: Double, currentPrice: Double? = nil, investmentDate: Date = Date()) {
        self.name = name
        self.symbol = symbol
        self.exchange = exchange
        self.quantity = quantity
        self.purchasePrice = purchasePrice
        self.currentPrice = currentPrice ?? purchasePrice
        self.investmentDate = investmentDate
        self.lastUpdated = Date()
        
        // Calculate stored values
        self._investedAmount = quantity * purchasePrice
        self._currentValue = quantity * self.currentPrice
    }
    
    // Method to update current price and recalculate current value
    mutating func updateCurrentPrice(_ newPrice: Double) {
        self.currentPrice = newPrice
        self._currentValue = quantity * newPrice
        self.lastUpdated = Date()
    }
}

// MARK: - Mutual Fund Model
struct MutualFund: Investment {
    let id = UUID()
    var name: String
    var schemeCode: String
    var units: Double
    var purchaseNAV: Double
    private var _currentNAV: Double
    var investmentDate: Date
    var lastUpdated: Date
    
    var currentNAV: Double {
        return _currentNAV
    }
    
    private var _investedAmount: Double {
        return units * purchaseNAV
    }
    
    private var _currentValue: Double {
        return units * _currentNAV
    }
    
    var investedAmount: Double {
        return _investedAmount
    }
    
    var currentValue: Double {
        return _currentValue
    }
    
    init(name: String, schemeCode: String, units: Double, purchaseNAV: Double, currentNAV: Double, investmentDate: Date = Date()) {
        self.name = name
        self.schemeCode = schemeCode
        self.units = units
        self.purchaseNAV = purchaseNAV
        self._currentNAV = currentNAV
        self.investmentDate = investmentDate
        self.lastUpdated = Date()
    }
    
    mutating func updateCurrentNAV(_ newNAV: Double) {
        _currentNAV = newNAV
        lastUpdated = Date()
    }
}

// MARK: - Crypto Model
struct Crypto: Investment {
    let id = UUID()
    var name: String
    var symbol: String
    var exchange: String
    var quantity: Double
    var purchasePrice: Double
    var currentPrice: Double
    var investmentDate: Date
    var lastUpdated: Date
    
    var investedAmount: Double {
        return quantity * purchasePrice
    }
    
    var currentValue: Double {
        return quantity * currentPrice
    }
    
    func updateCurrentPrice(_ newPrice: Double) -> Crypto {
        var updated = self
        updated.currentPrice = newPrice
        updated.lastUpdated = Date()
        return updated
    }
    
    init(name: String, symbol: String, exchange: String, quantity: Double, purchasePrice: Double, currentPrice: Double, investmentDate: Date = Date()) {
        self.name = name
        self.symbol = symbol
        self.exchange = exchange
        self.quantity = quantity
        self.purchasePrice = purchasePrice
        self.currentPrice = currentPrice
        self.investmentDate = investmentDate
        self.lastUpdated = Date()
    }
}

// MARK: - Fixed Deposit Model
struct FixedDeposit: Investment {
    let id = UUID()
    var name: String // Bank name
    private var _investedAmount: Double
    var interestRate: Double
    var investmentDate: Date
    var maturityDate: Date
    var compoundingFrequency: CompoundingFrequency
    
    enum CompoundingFrequency: String, CaseIterable, Codable {
        case monthly = "Monthly"
        case quarterly = "Quarterly"
        case halfYearly = "Half-Yearly"
        case annually = "Annually"
        
        var periodsPerYear: Double {
            switch self {
            case .monthly: return 12
            case .quarterly: return 4
            case .halfYearly: return 2
            case .annually: return 1
            }
        }
        
        var displayName: String {
            return rawValue
        }
    }
    
    var investedAmount: Double {
        return _investedAmount
    }
    
    // Calculated maturity amount using compound interest formula
    var maturityAmount: Double {
        let principal = _investedAmount
        let rate = interestRate / 100.0
        let timeInYears = yearsBetween(investmentDate, maturityDate)
        let n = compoundingFrequency.periodsPerYear
        
        // Compound Interest Formula: A = P(1 + r/n)^(nt)
        let amount = principal * pow(1 + (rate / n), n * timeInYears)
        return amount
    }
    
    var currentValue: Double {
        let now = Date()
        
        if now >= maturityDate {
            return maturityAmount
        } else {
            // Calculate current value based on time elapsed using compound interest
            let principal = _investedAmount
            let rate = interestRate / 100.0
            let timeInYears = yearsBetween(investmentDate, now)
            let n = compoundingFrequency.periodsPerYear
            
            guard timeInYears > 0 else { return principal }
            
            // Compound Interest Formula: A = P(1 + r/n)^(nt)
            let amount = principal * pow(1 + (rate / n), n * timeInYears)
            return amount
        }
    }
    
    var totalInterest: Double {
        return maturityAmount - _investedAmount
    }
    
    var currentInterest: Double {
        return currentValue - _investedAmount
    }
    
    init(bankName: String, investedAmount: Double, interestRate: Double, investmentDate: Date, maturityDate: Date, compoundingFrequency: CompoundingFrequency = .quarterly) {
        self.name = bankName
        self._investedAmount = investedAmount
        self.interestRate = interestRate
        self.investmentDate = investmentDate
        self.maturityDate = maturityDate
        self.compoundingFrequency = compoundingFrequency
    }
    
    // Helper function to calculate years between dates
    private func yearsBetween(_ startDate: Date, _ endDate: Date) -> Double {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: startDate, to: endDate)
        let days = components.day ?? 0
        return Double(days) / 365.25
    }
}

// MARK: - EPF Model
struct EPF: Investment {
    let id = UUID()
    var name: String = "Employee Provident Fund"
    private var _investedAmount: Double
    private var _currentValue: Double
    var investmentDate: Date
    var lastUpdated: Date
    
    var investedAmount: Double {
        return _investedAmount
    }
    
    var currentValue: Double {
        return _currentValue
    }
    
    init(investedAmount: Double, currentValue: Double, investmentDate: Date = Date()) {
        self._investedAmount = investedAmount
        self._currentValue = currentValue
        self.investmentDate = investmentDate
        self.lastUpdated = Date()
    }
}

// MARK: - NPS Model
struct NPS: Investment {
    let id = UUID()
    var name: String = "National Pension System"
    private var _investedAmount: Double
    private var _currentValue: Double
    var investmentDate: Date
    var lastUpdated: Date
    
    var investedAmount: Double {
        return _investedAmount
    }
    
    var currentValue: Double {
        return _currentValue
    }
    
    init(investedAmount: Double, currentValue: Double, investmentDate: Date = Date()) {
        self._investedAmount = investedAmount
        self._currentValue = currentValue
        self.investmentDate = investmentDate
        self.lastUpdated = Date()
    }
}

// MARK: - RSU Model
struct RSU: Investment {
    let id = UUID()
    var name: String // Company name
    private var _investedAmount: Double
    private var _currentValue: Double
    var numberOfStocks: Double
    var unitPriceUSD: Double // Price when granted/vested
    var currentPriceUSD: Double // Current market price per unit
    var exchangeRate: Double // USD to INR exchange rate
    var investmentDate: Date
    var lastUpdated: Date
    
    var investedAmount: Double {
        return _investedAmount
    }
    
    var currentValue: Double {
        return _currentValue
    }
    
    // Calculated properties for display
    var investedAmountUSD: Double {
        return numberOfStocks * unitPriceUSD
    }
    
    var currentAmountINR: Double {
        return numberOfStocks * currentPriceUSD * exchangeRate
    }
    
    init(companyName: String, numberOfStocks: Double, unitPriceUSD: Double, currentPriceUSD: Double, exchangeRate: Double, investmentDate: Date = Date()) {
        self.name = companyName
        self.numberOfStocks = numberOfStocks
        self.unitPriceUSD = unitPriceUSD
        self.currentPriceUSD = currentPriceUSD
        self.exchangeRate = exchangeRate
        self.investmentDate = investmentDate
        self.lastUpdated = Date()
        
        // Calculate and store the INR values for Investment protocol
        self._investedAmount = numberOfStocks * unitPriceUSD * exchangeRate
        self._currentValue = numberOfStocks * currentPriceUSD * exchangeRate
    }
}

// MARK: - Gold Model
struct Gold: Investment {
    let id = UUID()
    var name: String // Description/Name for this gold investment
    var grams: Double
    var purity: GoldPurity // 22kt or 24kt
    var purchaseRate: Double // Rate per gram when purchased
    var currentRate: Double // Current rate per gram
    var investmentDate: Date
    var totalPrice: Double // Total amount paid
    
    var investedAmount: Double {
        return totalPrice
    }
    
    var currentValue: Double {
        return grams * currentRate
    }
    
    // Calculated purchase rate per gram
    var calculatedPurchaseRate: Double {
        return totalPrice / grams
    }
}

// MARK: - Gold Purity Enum
enum GoldPurity: String, CaseIterable, Codable {
    case kt22 = "22kt"
    case kt24 = "24kt"
    
    var displayName: String {
        return self.rawValue
    }
    
    var factor: Double {
        switch self {
        case .kt22:
            return 0.916 // 22kt is 91.6% pure
        case .kt24:
            return 0.999 // 24kt is 99.9% pure
        }
    }
}

// MARK: - Investment Type Enum
enum InvestmentType: String, CaseIterable {
    case stocks = "Stocks"
    case mutualFunds = "Mutual Funds"
    case crypto = "Cryptocurrency"
    case fixedDeposits = "Fixed Deposits"
    case epf = "EPF"
    case nps = "NPS"
    case rsu = "RSU"
    case gold = "Gold"
    
    var icon: String {
        switch self {
        case .stocks: return "chart.line.uptrend.xyaxis"
        case .mutualFunds: return "building.columns.fill"
        case .crypto: return "bitcoinsign.circle.fill"
        case .fixedDeposits: return "banknote.fill"
        case .epf: return "person.fill.checkmark"
        case .nps: return "shield.fill"
        case .rsu: return "star.fill"
        case .gold: return "circle.fill"
        }
    }
    
    var color: String {
        switch self {
        case .stocks: return "blue"
        case .mutualFunds: return "green"
        case .crypto: return "orange"
        case .fixedDeposits: return "purple"
        case .epf: return "indigo"
        case .nps: return "teal"
        case .rsu: return "pink"
        case .gold: return "yellow"
        }
    }
}

// MARK: - Investment Validation
extension Investment {
    var isValid: Bool {
        return !name.isEmpty && investedAmount >= 0 && currentValue >= 0
    }
    
    var absoluteProfitLoss: Double {
        return abs(profitLoss)
    }
    
    var isProfit: Bool {
        return profitLoss >= 0
    }
    
    var formattedProfitLoss: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "INR"
        formatter.currencySymbol = "₹"
        return formatter.string(from: NSNumber(value: profitLoss)) ?? "₹0"
    }
    
    var formattedProfitLossPercentage: String {
        return String(format: "%.2f%%", profitLossPercentage)
    }
} 