import Foundation

// MARK: - Sample Data Factory
struct SampleDataFactory {
    static let shared = SampleDataFactory()
    
    private init() {}
    
    // MARK: - Sample Stocks
    func sampleStocks() -> [Stock] {
        return [
            Stock(
                name: "Reliance Industries Limited",
                symbol: "RELIANCE",
                exchange: "NSE",
                quantity: 10,
                purchasePrice: 2000.0,
                currentPrice: 2200.0,
                investmentDate: Calendar.current.date(byAdding: .month, value: -6, to: Date()) ?? Date()
            ),
            Stock(
                name: "Infosys Limited",
                symbol: "INFY",
                exchange: "NSE",
                quantity: 25,
                purchasePrice: 1500.0,
                currentPrice: 1650.0,
                investmentDate: Calendar.current.date(byAdding: .month, value: -4, to: Date()) ?? Date()
            ),
            Stock(
                name: "HDFC Bank Limited",
                symbol: "HDFCBANK",
                exchange: "NSE",
                quantity: 15,
                purchasePrice: 1400.0,
                currentPrice: 1380.0,
                investmentDate: Calendar.current.date(byAdding: .month, value: -3, to: Date()) ?? Date()
            ),
            Stock(
                name: "Tata Consultancy Services",
                symbol: "TCS",
                exchange: "NSE",
                quantity: 8,
                purchasePrice: 3200.0,
                currentPrice: 3400.0,
                investmentDate: Calendar.current.date(byAdding: .month, value: -8, to: Date()) ?? Date()
            )
        ]
    }
    
    // MARK: - Sample Mutual Funds
    func sampleMutualFunds() -> [MutualFund] {
        return [
            MutualFund(
                name: "SBI Bluechip Fund - Direct Plan - Growth",
                schemeCode: "120503",
                units: 150.5,
                purchaseNAV: 55.25,
                currentNAV: 62.80,
                investmentDate: Calendar.current.date(byAdding: .year, value: -1, to: Date()) ?? Date()
            ),
            MutualFund(
                name: "HDFC Index Fund - Nifty 50 Plan - Direct Plan - Growth",
                schemeCode: "120503",
                units: 200.0,
                purchaseNAV: 48.50,
                currentNAV: 52.30,
                investmentDate: Calendar.current.date(byAdding: .month, value: -10, to: Date()) ?? Date()
            ),
            MutualFund(
                name: "ICICI Prudential Technology Fund - Direct Plan - Growth",
                schemeCode: "119597",
                units: 75.25,
                purchaseNAV: 85.60,
                currentNAV: 89.40,
                investmentDate: Calendar.current.date(byAdding: .month, value: -7, to: Date()) ?? Date()
            )
        ]
    }
    
    // MARK: - Sample Crypto
    func sampleCryptos() -> [Crypto] {
        return [
            Crypto(
                name: "Bitcoin",
                symbol: "BTC",
                exchange: "CoinDCX",
                quantity: 0.1,
                purchasePrice: 2800000.0, // ₹28 Lakh per BTC
                currentPrice: 3200000.0, // ₹32 Lakh per BTC
                investmentDate: Calendar.current.date(byAdding: .month, value: -5, to: Date()) ?? Date()
            ),
            Crypto(
                name: "Ethereum",
                symbol: "ETH",
                exchange: "CoinDCX",
                quantity: 1.5,
                purchasePrice: 180000.0, // ₹1.8 Lakh per ETH
                currentPrice: 190000.0, // ₹1.9 Lakh per ETH
                investmentDate: Calendar.current.date(byAdding: .month, value: -3, to: Date()) ?? Date()
            )
        ]
    }
    
    // MARK: - Sample Fixed Deposits
    func sampleFixedDeposits() -> [FixedDeposit] {
        let startDate = Calendar.current.date(byAdding: .year, value: -1, to: Date()) ?? Date()
        let maturityDate = Calendar.current.date(byAdding: .year, value: 2, to: startDate) ?? Date()
        
        return [
            FixedDeposit(
                bankName: "HDFC Bank",
                investedAmount: 100000.0,
                interestRate: 6.5,
                investmentDate: startDate,
                maturityDate: maturityDate,
                compoundingFrequency: .quarterly
            ),
            FixedDeposit(
                bankName: "SBI Bank",
                investedAmount: 200000.0,
                interestRate: 6.8,
                investmentDate: Calendar.current.date(byAdding: .month, value: -8, to: Date()) ?? Date(),
                maturityDate: Calendar.current.date(byAdding: .year, value: 3, to: Date()) ?? Date(),
                compoundingFrequency: .monthly
            ),
            FixedDeposit(
                bankName: "ICICI Bank",
                investedAmount: 150000.0,
                interestRate: 6.2,
                investmentDate: Calendar.current.date(byAdding: .month, value: -6, to: Date()) ?? Date(),
                maturityDate: Calendar.current.date(byAdding: .year, value: 2, to: Date()) ?? Date(),
                compoundingFrequency: .halfYearly
            )
        ]
    }
    
    // MARK: - Sample EPF
    func sampleEPF() -> [EPF] {
        return [
            EPF(
                investedAmount: 450000.0,
                currentValue: 520000.0,
                investmentDate: Calendar.current.date(byAdding: .year, value: -3, to: Date()) ?? Date()
            )
        ]
    }
    
    // MARK: - Sample NPS
    func sampleNPS() -> [NPS] {
        return [
            NPS(
                investedAmount: 180000.0,
                currentValue: 210000.0,
                investmentDate: Calendar.current.date(byAdding: .year, value: -2, to: Date()) ?? Date()
            )
        ]
    }
    
    // MARK: - Sample RSUs
    func sampleRSUs() -> [RSU] {
        return [
            RSU(
                companyName: "Tech Corp",
                numberOfStocks: 100,
                unitPriceUSD: 120.0, // $120 per unit at grant
                currentPriceUSD: 150.0, // $150 current price per unit
                exchangeRate: 83.25, // USD to INR exchange rate
                investmentDate: Calendar.current.date(byAdding: .year, value: -1, to: Date()) ?? Date()
            ),
            RSU(
                companyName: "Software Solutions Ltd",
                numberOfStocks: 50,
                unitPriceUSD: 200.0, // $200 per unit at grant
                currentPriceUSD: 220.0, // $220 current price per unit
                exchangeRate: 83.25, // USD to INR exchange rate
                investmentDate: Calendar.current.date(byAdding: .month, value: -6, to: Date()) ?? Date()
            )
        ]
    }
    
    // MARK: - Sample Gold
    func sampleGold() -> [Gold] {
        return [
            Gold(
                name: "22kt Gold Coins",
                grams: 10.0,
                purity: .kt22,
                purchaseRate: 8500.0, // ₹8500 per gram (calculated)
                currentRate: 8774.0, // Current 22kt rate
                investmentDate: Calendar.current.date(byAdding: .year, value: -1, to: Date()) ?? Date(),
                totalPrice: 85000.0 // Total paid: 10g × ₹8500
            ),
            Gold(
                name: "24kt Gold Bars",
                grams: 5.0,
                purity: .kt24,
                purchaseRate: 9200.0, // ₹9200 per gram (calculated)
                currentRate: 9578.0, // Current 24kt rate
                investmentDate: Calendar.current.date(byAdding: .month, value: -8, to: Date()) ?? Date(),
                totalPrice: 46000.0 // Total paid: 5g × ₹9200
            ),
            Gold(
                name: "22kt Gold Jewelry",
                grams: 15.5,
                purity: .kt22,
                purchaseRate: 8600.0, // ₹8600 per gram (calculated)
                currentRate: 8774.0, // Current 22kt rate
                investmentDate: Calendar.current.date(byAdding: .month, value: -4, to: Date()) ?? Date(),
                totalPrice: 133300.0 // Total paid: 15.5g × ₹8600
            )
        ]
    }
    
    // MARK: - Complete Sample Portfolio
    func createSamplePortfolio() -> (
        stocks: [Stock],
        mutualFunds: [MutualFund],
        cryptos: [Crypto],
        fixedDeposits: [FixedDeposit],
        epf: [EPF],
        nps: [NPS],
        rsus: [RSU],
        gold: [Gold]
    ) {
        return (
            stocks: sampleStocks(),
            mutualFunds: sampleMutualFunds(),
            cryptos: sampleCryptos(),
            fixedDeposits: sampleFixedDeposits(),
            epf: sampleEPF(),
            nps: sampleNPS(),
            rsus: sampleRSUs(),
            gold: sampleGold()
        )
    }
}

 