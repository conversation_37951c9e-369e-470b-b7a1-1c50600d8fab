import SwiftUI

struct ContentView: View {
    @StateObject private var portfolioViewModel = PortfolioViewModel()
    
    var body: some View {
        TabView {
            PortfolioView(portfolioViewModel: portfolioViewModel)
                .tabItem {
                    Image(systemName: "chart.pie.fill")
                    Text("Portfolio")
                }
            
            StocksView(portfolioViewModel: portfolioViewModel)
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("Stocks")
                }
            
            MutualFundsView(portfolioViewModel: portfolioViewModel)
                .tabItem {
                    Image(systemName: "building.columns.fill")
                    Text("Mutual Funds")
                }
            
            CryptoView(portfolioViewModel: portfolioViewModel)
                .tabItem {
                    Image(systemName: "bitcoinsign.circle.fill")
                    Text("Crypto")
                }
            
            FixedDepositsView(portfolioViewModel: portfolioViewModel)
                .tabItem {
                    Image(systemName: "banknote.fill")
                    Text("FDs")
                }
            
            EPFView(portfolioViewModel: portfolioViewModel)
                .tabItem {
                    Image(systemName: "person.crop.circle.fill")
                    Text("EPF")
                }
            
            NPSView(portfolioViewModel: portfolioViewModel)
                .tabItem {
                    Image(systemName: "shield.fill")
                    Text("NPS")
                }
            
            RSUView(portfolioViewModel: portfolioViewModel)
                .tabItem {
                    Image(systemName: "star.fill")
                    Text("RSU")
                }
            
            GoldView(portfolioViewModel: portfolioViewModel)
                .tabItem {
                    Image(systemName: "circle.fill")
                    Text("Gold")
                }
        }
        .accentColor(.blue)
        .onAppear {
            portfolioViewModel.refreshPortfolioValues()
        }
    }
}

#Preview {
    ContentView()
} 