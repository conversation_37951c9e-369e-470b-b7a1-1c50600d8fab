import Foundation
import Security

final class KeychainManager: @unchecked Sendable {
    static let shared = KeychainManager()
    
    private init() {}
    
    private let service = "com.investmenttracker.api"
    
    // MARK: - Keychain Keys
    private enum KeychainKeys {
        static let coinDCXAPIKey = "coindcx_api_key"
        static let coinDCXAPISecret = "coindcx_api_secret"
    }
    
    // MARK: - CoinDCX Credentials
    func saveCoinDCXCredentials(apiKey: String, apiSecret: String) -> Bool {
        let saveKeyResult = saveToKeychain(key: KeychainKeys.coinDCXAPIKey, value: apiKey)
        let saveSecretResult = saveToKeychain(key: KeychainKeys.coinDCXAPISecret, value: apiSecret)
        
        return saveKeyResult && saveSecretResult
    }
    
    func getCoinDCXCredentials() -> (apiKey: String?, apiSecret: String?) {
        let apiKey = getFromKeychain(key: KeychainKeys.coinDCXAPIKey)
        let apiSecret = getFromKeychain(key: KeychainKeys.coinDCXAPISecret)
        
        return (apiKey, apiSecret)
    }
    
    func deleteCoinDCXCredentials() -> Bool {
        let deleteKeyResult = deleteFromKeychain(key: KeychainKeys.coinDCXAPIKey)
        let deleteSecretResult = deleteFromKeychain(key: KeychainKeys.coinDCXAPISecret)
        
        return deleteKeyResult && deleteSecretResult
    }
    
    func hasCoinDCXCredentials() -> Bool {
        let credentials = getCoinDCXCredentials()
        return credentials.apiKey != nil && credentials.apiSecret != nil &&
               !credentials.apiKey!.isEmpty && !credentials.apiSecret!.isEmpty
    }
    
    // MARK: - Generic Keychain Operations
    private func saveToKeychain(key: String, value: String) -> Bool {
        guard let data = value.data(using: .utf8) else {
            return false
        }
        
        // Delete any existing item first
        _ = deleteFromKeychain(key: key)
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        let status = SecItemAdd(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    private func getFromKeychain(key: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess,
              let data = result as? Data,
              let string = String(data: data, encoding: .utf8) else {
            return nil
        }
        
        return string
    }
    
    private func deleteFromKeychain(key: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        return status == errSecSuccess || status == errSecItemNotFound
    }
    
    // MARK: - Validation
    func validateCoinDCXCredentials() -> Bool {
        let credentials = getCoinDCXCredentials()
        
        guard let apiKey = credentials.apiKey,
              let apiSecret = credentials.apiSecret else {
            return false
        }
        
        // Basic validation
        return !apiKey.isEmpty && !apiSecret.isEmpty &&
               apiKey.count > 10 && apiSecret.count > 10
    }
    
    // MARK: - Debug/Development Methods
    #if DEBUG
    func clearAllCredentials() -> Bool {
        return deleteCoinDCXCredentials()
    }
    
    func printStoredCredentials() {
        let credentials = getCoinDCXCredentials()
        print("CoinDCX API Key: \(credentials.apiKey?.prefix(10) ?? "nil")...")
        print("CoinDCX API Secret: \(credentials.apiSecret?.prefix(10) ?? "nil")...")
    }
    #endif
}

// MARK: - Keychain Error Handling
extension KeychainManager {
    enum KeychainError: LocalizedError {
        case itemNotFound
        case duplicateItem
        case invalidItemFormat
        case unexpectedStatus(OSStatus)
        
        var errorDescription: String? {
            switch self {
            case .itemNotFound:
                return "Keychain item not found"
            case .duplicateItem:
                return "Duplicate keychain item"
            case .invalidItemFormat:
                return "Invalid keychain item format"
            case .unexpectedStatus(let status):
                return "Unexpected keychain status: \(status)"
            }
        }
    }
    
    private func handleKeychainError(_ status: OSStatus) -> KeychainError {
        switch status {
        case errSecItemNotFound:
            return .itemNotFound
        case errSecDuplicateItem:
            return .duplicateItem
        default:
            return .unexpectedStatus(status)
        }
    }
}
