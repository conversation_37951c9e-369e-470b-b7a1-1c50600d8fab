import Foundation

@MainActor
class GoldPriceService: ObservableObject {
    static let shared = GoldPriceService()
    
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var lastUpdated: Date = Date()
    @Published var currentPrice22kt: Double = 8774.0 // 22kt price in INR per gram
    @Published var currentPrice24kt: Double = 9578.0 // 24kt price in INR per gram
    
    // Using Gold API for live gold prices
    private let goldAPIURL = "https://api.metals.live/v1/spot/gold"
    private let fallbackAPIURL = "https://api.coindesk.com/v1/bpi/currentprice.json" // Bitcoin price as fallback for demo
    
    private init() {}
    
    // MARK: - Public Methods
    
    /// Fetch current gold prices per gram in INR for both 22kt and 24kt
    func fetchCurrentGoldPrices() async -> (kt22: Double, kt24: Double)? {
        isLoading = true
        errorMessage = nil
        
        do {
            let prices = try await fetchFromGoldAPI()
            currentPrice22kt = prices.kt22
            currentPrice24kt = prices.kt24
            lastUpdated = Date()
            print("✅ Gold prices updated: 22kt ₹\(String(format: "%.2f", prices.kt22)), 24kt ₹\(String(format: "%.2f", prices.kt24)) per gram")
            isLoading = false
            return prices
        } catch {
            print("❌ Gold API failed: \(error.localizedDescription)")
            
            // Use fallback with current prices + small random variation for demo
            let variation = Double.random(in: -20...20)
            let fallback22kt = 8774.0 + variation
            let fallback24kt = 9578.0 + variation
            
            currentPrice22kt = fallback22kt
            currentPrice24kt = fallback24kt
            lastUpdated = Date()
            errorMessage = "Using estimated prices. Live data unavailable."
            
            print("✅ Using fallback gold prices: 22kt ₹\(String(format: "%.2f", fallback22kt)), 24kt ₹\(String(format: "%.2f", fallback24kt)) per gram")
            isLoading = false
            return (kt22: fallback22kt, kt24: fallback24kt)
        }
    }
    
    /// Refresh gold prices synchronously
    func refreshGoldPrices() {
        Task {
            await fetchCurrentGoldPrices()
        }
    }
    
    /// Get current price for specific purity
    func getCurrentPrice(for purity: GoldPurity) -> Double {
        switch purity {
        case .kt22:
            return currentPrice22kt > 0 ? currentPrice22kt : 8774.0
        case .kt24:
            return currentPrice24kt > 0 ? currentPrice24kt : 9578.0
        }
    }
    
    // MARK: - Private Methods
    
    private func fetchFromGoldAPI() async throws -> (kt22: Double, kt24: Double) {
        // For demo purposes, we'll simulate a gold price API call
        // In a real implementation, you would integrate with:
        // - IBJA (India Bullion and Jewellers Association) API
        // - goldapi.io for Indian market
        // - metals.live API
        
        // Simulate API delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Simulate current Indian gold prices with some variation
        // Based on current IBJA rates
        let base22ktPrice = 8774.0 // INR per gram for 22kt (916 purity)
        let base24ktPrice = 9578.0 // INR per gram for 24kt (999 purity)
        
        let variation = Double.random(in: -50...50)
        let simulated22kt = max(base22ktPrice + variation, 8000.0)
        let simulated24kt = max(base24ktPrice + variation, 9000.0)
        
        return (kt22: simulated22kt, kt24: simulated24kt)
    }
    
    /// Get gold price history (mock data for demo)
    func getGoldPriceHistory() -> [(date: Date, price: Double)] {
        let calendar = Calendar.current
        var history: [(Date, Double)] = []
        
        for i in 0..<30 {
            if let date = calendar.date(byAdding: .day, value: -i, to: Date()) {
                let basePrice = 5450.0
                let variation = Double.random(in: -200...200)
                let price = max(basePrice + variation, 5000.0)
                history.append((date, price))
            }
        }
        
        return history.reversed()
    }
    
    /// Get popular gold investment options
    func getGoldInvestmentOptions() -> [GoldInvestmentOption] {
        return [
            GoldInvestmentOption(
                type: "Physical Gold",
                description: "Gold coins, bars, jewelry",
                advantages: ["Tangible asset", "No counterparty risk", "Cultural value"],
                disadvantages: ["Storage costs", "Making charges", "Liquidity issues"]
            ),
            GoldInvestmentOption(
                type: "Gold ETF",
                description: "Exchange Traded Funds backed by gold",
                advantages: ["High liquidity", "No storage costs", "Low expense ratio"],
                disadvantages: ["Demat account required", "Market timing risk"]
            ),
            GoldInvestmentOption(
                type: "Gold Mutual Funds",
                description: "Mutual funds investing in gold ETFs",
                advantages: ["Professional management", "SIP available", "No demat needed"],
                disadvantages: ["Higher expense ratio", "Fund manager risk"]
            ),
            GoldInvestmentOption(
                type: "Digital Gold",
                description: "Online platforms for gold investment",
                advantages: ["Fractional investment", "Easy buying/selling", "Secure storage"],
                disadvantages: ["Platform risk", "Limited regulation"]
            )
        ]
    }
}

// MARK: - Data Models

struct GoldInvestmentOption {
    let type: String
    let description: String
    let advantages: [String]
    let disadvantages: [String]
}

// MARK: - Error Types

enum GoldPriceError: LocalizedError {
    case invalidURL
    case invalidResponse
    case noData
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL for gold price API"
        case .invalidResponse:
            return "Invalid response from gold price API"
        case .noData:
            return "No gold price data available"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
} 