import Foundation

@MainActor
class CryptoPriceService: ObservableObject {
    static let shared = CryptoPriceService()
    
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var searchResults: [CryptoCoin] = []
    @Published var popularCoins: [CryptoCoin] = []
    
    private let coinGeckoBaseURL = "https://api.coingecko.com/api/v3"
    private let coinDCXBaseURL = "https://api.coindcx.com/exchange/ticker"
    
    private init() {
        Task {
            await loadPopularCoins()
        }
    }
    
    func loadPopularCoins() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Use CoinGecko for listing popular coins
            let url = URL(string: "\(coinGeckoBaseURL)/coins/markets?vs_currency=inr&order=market_cap_desc&per_page=50&page=1&sparkline=false")!
            let (data, _) = try await URLSession.shared.data(from: url)
            let coins = try JSONDecoder().decode([CoinGeckoMarketData].self, from: data)
            
            let cryptoCoins = coins.map { coin in
                CryptoCoin(
                    id: coin.id,
                    name: coin.name,
                    symbol: coin.symbol.uppercased(),
                    currentPrice: coin.current_price,
                    exchange: "CoinDCX"
                )
            }
            
            self.popularCoins = cryptoCoins
            self.searchResults = cryptoCoins
            
            print("Loaded \(cryptoCoins.count) popular coins from CoinGecko")
            
        } catch {
            self.errorMessage = "Failed to load coins: \(error.localizedDescription)"
            print("Error loading popular coins: \(error)")
        }
        
        isLoading = false
    }
    
    func searchCoins(query: String) async {
        guard !query.isEmpty else {
            searchResults = popularCoins
            return
        }
        
        isLoading = true
        
        do {
            // Use CoinGecko search API
            let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? query
            let url = URL(string: "\(coinGeckoBaseURL)/search?query=\(encodedQuery)")!
            let (data, _) = try await URLSession.shared.data(from: url)
            let searchResponse = try JSONDecoder().decode(CoinGeckoSearchResponse.self, from: data)
            
            let cryptoCoins = searchResponse.coins.prefix(20).map { coin in
                CryptoCoin(
                    id: coin.id,
                    name: coin.name,
                    symbol: coin.symbol.uppercased(),
                    currentPrice: 0, // Will be fetched when selected
                    exchange: "CoinDCX"
                )
            }
            
            searchResults = Array(cryptoCoins)
            
        } catch {
            self.errorMessage = "Search failed: \(error.localizedDescription)"
            print("Error searching coins: \(error)")
        }
        
        isLoading = false
    }
    
    // Add a new result structure to return both price and debug info
    struct PriceResult {
        let price: Double?
        let debugInfo: String
    }
    
    func fetchCurrentPriceWithDebug(for symbol: String) async -> PriceResult {
        let coinDCXSymbol = "\(symbol.uppercased())INR"
        var debugInfo = "🔍 Fetching: \(symbol) -> \(coinDCXSymbol)\n"
        
        do {
            let url = URL(string: coinDCXBaseURL)!
            
            var request = URLRequest(url: url)
            request.setValue("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", forHTTPHeaderField: "User-Agent")
            
            debugInfo += "📡 URL: \(coinDCXBaseURL)\n"
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                debugInfo += "📡 Response Code: \(httpResponse.statusCode)\n"
                debugInfo += "📊 Data Size: \(data.count) bytes\n"
            }
            
            // Convert to string and search for our symbol directly
            guard let responseString = String(data: data, encoding: .utf8) else {
                debugInfo += "❌ Could not convert response to string\n"
                return PriceResult(price: nil, debugInfo: debugInfo)
            }
            
            debugInfo += "📊 Response Length: \(responseString.count) characters\n"
            
            // Search for our symbol in the raw string using a simpler approach
            let searchPattern = "\"market\":\"\(coinDCXSymbol)\""
            if let symbolRange = responseString.range(of: searchPattern) {
                debugInfo += "✅ Found \(coinDCXSymbol) in response\n"
                
                // Find the start of this ticker object by looking backwards for '{'
                let startSearchIndex = max(responseString.startIndex, responseString.index(symbolRange.lowerBound, offsetBy: -100, limitedBy: responseString.startIndex) ?? responseString.startIndex)
                let beforeSymbol = String(responseString[startSearchIndex..<symbolRange.lowerBound])
                
                guard let lastBraceIndex = beforeSymbol.lastIndex(of: "{") else {
                    debugInfo += "❌ Could not find start of ticker object\n"
                    return PriceResult(price: nil, debugInfo: debugInfo)
                }
                
                let tickerStartIndex = responseString.index(startSearchIndex, offsetBy: beforeSymbol.distance(from: beforeSymbol.startIndex, to: lastBraceIndex))
                
                // Find the end of this ticker object by looking forwards for '}'
                let endSearchIndex = min(responseString.endIndex, responseString.index(symbolRange.upperBound, offsetBy: 500, limitedBy: responseString.endIndex) ?? responseString.endIndex)
                let afterSymbol = String(responseString[symbolRange.upperBound..<endSearchIndex])
                
                guard let firstBraceIndex = afterSymbol.firstIndex(of: "}") else {
                    debugInfo += "❌ Could not find end of ticker object\n"
                    return PriceResult(price: nil, debugInfo: debugInfo)
                }
                
                let tickerEndIndex = responseString.index(symbolRange.upperBound, offsetBy: afterSymbol.distance(from: afterSymbol.startIndex, to: firstBraceIndex))
                
                // Extract the ticker JSON
                let tickerJSON = String(responseString[tickerStartIndex...tickerEndIndex])
                debugInfo += "📊 Ticker JSON: \(tickerJSON.prefix(200))...\n"
                
                // Parse just this ticker
                if let tickerData = tickerJSON.data(using: .utf8) {
                    do {
                        let ticker = try JSONDecoder().decode(CoinDCXTicker.self, from: tickerData)
                        debugInfo += "✅ Successfully parsed ticker\n"
                        if let price = Double(ticker.last_price), price > 0 {
                            debugInfo += "💰 Price: ₹\(price)\n"
                            return PriceResult(price: price, debugInfo: debugInfo)
                        } else {
                            debugInfo += "❌ Invalid price value: \(ticker.last_price)\n"
                        }
                    } catch {
                        debugInfo += "❌ JSON parsing error: \(error.localizedDescription)\n"
                    }
                } else {
                    debugInfo += "❌ Could not convert ticker to data\n"
                }
            } else {
                debugInfo += "❌ \(coinDCXSymbol) not found in response\n"
                // Show first 500 characters to debug
                debugInfo += "📊 Response start: \(responseString.prefix(500))\n"
            }
            
            return PriceResult(price: nil, debugInfo: debugInfo)
            
        } catch {
            debugInfo += "❌ Error: \(error.localizedDescription)\n"
            return PriceResult(price: nil, debugInfo: debugInfo)
        }
    }
    
    func fetchCurrentPrice(for symbol: String) async -> Double? {
        let result = await fetchCurrentPriceWithDebug(for: symbol)
        print(result.debugInfo) // Still log to console for development
        return result.price
    }
    
    func getAvailableExchanges() -> [String] {
        return ["CoinDCX"]
    }
}

// MARK: - Data Models
struct CryptoCoin {
    let id: String
    let name: String
    let symbol: String
    let currentPrice: Double
    let exchange: String
}

struct CoinDCXTicker: Codable {
    let market: String
    let last_price: String
    
    // Add custom decoder to handle potential parsing issues
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // Handle market field
        if let marketValue = try? container.decode(String.self, forKey: .market) {
            self.market = marketValue
        } else {
            throw DecodingError.dataCorrupted(DecodingError.Context(codingPath: [CodingKeys.market], debugDescription: "Market field missing or invalid"))
        }
        
        // Handle last_price field - try as String first, then as Double
        if let priceString = try? container.decode(String.self, forKey: .last_price) {
            self.last_price = priceString
        } else if let priceDouble = try? container.decode(Double.self, forKey: .last_price) {
            self.last_price = String(priceDouble)
        } else {
            throw DecodingError.dataCorrupted(DecodingError.Context(codingPath: [CodingKeys.last_price], debugDescription: "Last price field missing or invalid"))
        }
    }
    
    private enum CodingKeys: String, CodingKey {
        case market
        case last_price
    }
}

struct CoinGeckoMarketData: Codable {
    let id: String
    let symbol: String
    let name: String
    let current_price: Double
}

struct CoinGeckoSearchResponse: Codable {
    let coins: [CoinGeckoSearchCoin]
}

struct CoinGeckoSearchCoin: Codable {
    let id: String
    let name: String
    let symbol: String
}