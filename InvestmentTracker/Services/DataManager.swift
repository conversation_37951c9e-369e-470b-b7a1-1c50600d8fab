import Foundation

@MainActor
class DataManager: ObservableObject, @unchecked Sendable {
    static let shared = DataManager()
    
    private let userDefaults = UserDefaults.standard
    
    // MARK: - Keys for UserDefaults
    private enum Keys {
        static let stocks = "stocks"
        static let mutualFunds = "mutualFunds"
        static let cryptos = "cryptos"
        static let fixedDeposits = "fixedDeposits"
        static let epf = "epf"
        static let nps = "nps"
        static let rsus = "rsus"
        static let gold = "gold"
    }
    
    private init() {}
    
    // MARK: - Save Methods
    func save<T: Codable>(_ items: [T], forKey key: String) {
        do {
            let data = try JSONEncoder().encode(items)
            userDefaults.set(data, forKey: key)
        } catch {
            print("Failed to save \(key): \(error)")
        }
    }
    
    func saveStocks(_ stocks: [Stock]) {
        save(stocks, forKey: Keys.stocks)
    }
    
    func saveMutualFunds(_ mutualFunds: [MutualFund]) {
        save(mutualFunds, forKey: Keys.mutualFunds)
    }
    
    func saveCryptos(_ cryptos: [Crypto]) {
        save(cryptos, forKey: Keys.cryptos)
    }
    
    func saveFixedDeposits(_ fixedDeposits: [FixedDeposit]) {
        save(fixedDeposits, forKey: Keys.fixedDeposits)
    }
    
    func saveEPF(_ epf: [EPF]) {
        save(epf, forKey: Keys.epf)
    }
    
    func saveNPS(_ nps: [NPS]) {
        save(nps, forKey: Keys.nps)
    }
    
    func saveRSUs(_ rsus: [RSU]) {
        save(rsus, forKey: Keys.rsus)
    }
    
    func saveGold(_ gold: [Gold]) {
        save(gold, forKey: Keys.gold)
    }
    
    // MARK: - Load Methods
    func load<T: Codable>(_ type: T.Type, forKey key: String) -> [T] {
        guard let data = userDefaults.data(forKey: key) else {
            return []
        }
        
        do {
            return try JSONDecoder().decode([T].self, from: data)
        } catch {
            print("Failed to load \(key): \(error)")
            return []
        }
    }
    
    func loadStocks() -> [Stock] {
        return load(Stock.self, forKey: Keys.stocks)
    }
    
    func loadMutualFunds() -> [MutualFund] {
        return load(MutualFund.self, forKey: Keys.mutualFunds)
    }
    
    func loadCryptos() -> [Crypto] {
        return load(Crypto.self, forKey: Keys.cryptos)
    }
    
    func loadFixedDeposits() -> [FixedDeposit] {
        return load(FixedDeposit.self, forKey: Keys.fixedDeposits)
    }
    
    func loadEPF() -> [EPF] {
        return load(EPF.self, forKey: Keys.epf)
    }
    
    func loadNPS() -> [NPS] {
        return load(NPS.self, forKey: Keys.nps)
    }
    
    func loadRSUs() -> [RSU] {
        return load(RSU.self, forKey: Keys.rsus)
    }
    
    func loadGold() -> [Gold] {
        return load(Gold.self, forKey: Keys.gold)
    }
    
    // MARK: - Clear Methods
    func clearAllData() {
        let keys = [Keys.stocks, Keys.mutualFunds, Keys.cryptos, Keys.fixedDeposits,
                   Keys.epf, Keys.nps, Keys.rsus, Keys.gold]
        
        keys.forEach { userDefaults.removeObject(forKey: $0) }
    }
    
    // MARK: - Export/Import Methods
    func exportData() -> [String: Any] {
        return [
            Keys.stocks: loadStocks(),
            Keys.mutualFunds: loadMutualFunds(),
            Keys.cryptos: loadCryptos(),
            Keys.fixedDeposits: loadFixedDeposits(),
            Keys.epf: loadEPF(),
            Keys.nps: loadNPS(),
            Keys.rsus: loadRSUs(),
            Keys.gold: loadGold()
        ]
    }
    
    // TODO: Implement import functionality
    func importData(_ data: [String: Any]) {
        // Implementation for importing data from exported format
    }
} 