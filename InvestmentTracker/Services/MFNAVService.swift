import Foundation

@MainActor
class MFNAVService: ObservableObject {
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // Cache for mutual funds list
    private var cachedFunds: [MFSearchResult] = []
    private var lastFetchTime: Date?
    private let cacheValidityDuration: TimeInterval = 24 * 60 * 60 // 24 hours
    
    // Using MFAPI as primary source for both NAV and fund search
    private let baseURL = "https://api.mfapi.in"
    
    func searchMutualFunds(query: String) async -> [MFSearchResult] {
        isLoading = true
        errorMessage = nil
        defer { isLoading = false }
        
        // If query is empty, return popular/cached funds
        if query.isEmpty {
            await fetchPopularFunds()
            return Array(cachedFunds.prefix(20))
        }
        
        // Search for specific funds using API
        return await searchFundsFromAPI(query: query)
    }
    
    private func fetchPopularFunds() async {
        // Check if cache is still valid
        if let lastFetch = lastFetchTime,
           Date().timeIntervalSince(lastFetch) < cacheValidityDuration,
           !cachedFunds.isEmpty {
            return
        }
        
        // Fetch all funds from API
        guard let url = URL(string: "\(baseURL)/mf") else { return }
        
        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            let allFunds = try JSONDecoder().decode([MFListItem].self, from: data)
            
            // Filter for Direct Growth plans and popular fund houses
            let popularFundHouses = [
                "SBI", "ICICI", "HDFC", "Axis", "Kotak", "Mirae", "Parag Parikh",
                "Nippon", "UTI", "Aditya Birla", "Franklin", "DSP", "Invesco",
                "Quant", "Motilal Oswal", "PGIM", "Tata", "Mahindra", "Canara Robeco"
            ]
            
            let filteredFunds = allFunds.filter { fund in
                fund.schemeName.contains("Direct") &&
                fund.schemeName.contains("Growth") &&
                !fund.schemeName.contains("IDCW") &&
                popularFundHouses.contains { fundHouse in
                    fund.schemeName.localizedCaseInsensitiveContains(fundHouse)
                }
            }
            
            // Convert to MFSearchResult
            cachedFunds = filteredFunds.map { fund in
                MFSearchResult(
                    schemeCode: String(fund.schemeCode),
                    schemeName: fund.schemeName
                )
            }
            
            lastFetchTime = Date()
            
        } catch {
            errorMessage = "Failed to fetch mutual funds list: \(error.localizedDescription)"
            // Fallback to minimal popular funds if API fails
            cachedFunds = getFallbackPopularFunds()
        }
    }
    
    private func searchFundsFromAPI(query: String) async -> [MFSearchResult] {
        // If we have cached funds, search in cache first
        if !cachedFunds.isEmpty {
            let filtered = cachedFunds.filter { fund in
                fund.schemeName.localizedCaseInsensitiveContains(query) ||
                fund.schemeCode.contains(query)
            }
            if !filtered.isEmpty {
                return Array(filtered.prefix(10))
            }
        }
        
        // If no cached results or no matches, fetch from API
        await fetchPopularFunds()
        
        let filtered = cachedFunds.filter { fund in
            fund.schemeName.localizedCaseInsensitiveContains(query) ||
            fund.schemeCode.contains(query)
        }
        
        return Array(filtered.prefix(10))
    }
    
    func fetchNAV(schemeCode: String) async -> Double? {
        isLoading = true
        errorMessage = nil
        defer { isLoading = false }
        
        // Try multiple APIs in order of reliability
        
        // 1. Try RapidAPI MF API (most reliable for Indian MFs)
        if let nav = await fetchFromRapidAPI(schemeCode: schemeCode) {
            return nav
        }
        
        // 2. Try original MFAPI for AMFI codes
        if let nav = await fetchFromMFAPI(schemeCode: schemeCode) {
            return nav
        }
        
        // 3. Try MFCentral API as fallback
        if let nav = await fetchFromMFCentral(schemeCode: schemeCode) {
            return nav
        }
        
        errorMessage = "Unable to fetch NAV from available APIs. Please verify the scheme code and try again."
        return nil
    }
    
    private func fetchFromMFAPI(schemeCode: String) async -> Double? {
        guard let url = URL(string: "https://api.mfapi.in/mf/\(schemeCode)") else {
            return nil
        }
        
        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            let response = try JSONDecoder().decode(MFAPIResponse.self, from: data)
            
            if let latestData = response.data.first,
               let nav = Double(latestData.nav) {
                return nav
            }
        } catch {
            // Silently fail and try next API
        }
        
        return nil
    }
    
    private func fetchFromRapidAPI(schemeCode: String) async -> Double? {
        // Using MFCentral API which provides reliable NAV data
        guard let url = URL(string: "https://latest-mutual-fund-nav.p.rapidapi.com/fetchLatestNAV") else {
            return nil
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("latest-mutual-fund-nav.p.rapidapi.com", forHTTPHeaderField: "X-RapidAPI-Host")
        request.addValue("your-rapidapi-key", forHTTPHeaderField: "X-RapidAPI-Key") // Would need actual key
        
        let requestBody = ["schemeCode": schemeCode]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
            let (data, _) = try await URLSession.shared.data(for: request)
            
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let navValue = json["nav"] as? Double {
                return navValue
            }
        } catch {
            // Silently fail and try next API
        }
        
        return nil
    }
    
    private func fetchFromMFCentral(schemeCode: String) async -> Double? {
        // Alternative free API for Indian mutual funds
        guard let url = URL(string: "https://api.mfapi.in/mf/\(schemeCode)/latest") else {
            return nil
        }
        
        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let dataArray = json["data"] as? [[String: Any]],
               let latestData = dataArray.first,
               let navString = latestData["nav"] as? String,
               let nav = Double(navString) {
                return nav
            }
        } catch {
            // Silently fail
        }
        
        return nil
    }
    

    
    func fetchMultipleNAVs(schemeCodes: [String]) async -> [String: Double] {
        var results: [String: Double] = [:]
        
        isLoading = true
        errorMessage = nil
        defer { isLoading = false }
        
        await withTaskGroup(of: (String, Double?).self) { group in
            for schemeCode in schemeCodes {
                group.addTask {
                    let nav = await self.fetchNAVSilently(schemeCode: schemeCode)
                    return (schemeCode, nav)
                }
            }
            
            for await (schemeCode, nav) in group {
                if let nav = nav {
                    results[schemeCode] = nav
                }
            }
        }
        
        return results
    }
    
    private func fetchNAVSilently(schemeCode: String) async -> Double? {
        // Try multiple APIs silently for batch operations
        
        // Try MFAPI first (most reliable for AMFI codes)
        if let nav = await fetchFromMFAPI(schemeCode: schemeCode) {
            return nav
        }
        
        // Try MFCentral as fallback
        if let nav = await fetchFromMFCentral(schemeCode: schemeCode) {
            return nav
        }
        
        return nil
    }
    
    private func getFallbackPopularFunds() -> [MFSearchResult] {
        return [
            // Multi Cap / Flexi Cap Funds (Popular) - Using actual AMFI scheme codes
            MFSearchResult(schemeCode: "125498", schemeName: "Parag Parikh Flexi Cap Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "120716", schemeName: "Axis Multicap Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "118834", schemeName: "Kotak Standard Multicap Fund - Direct Plan - Growth"),
            
            // Large Cap Funds
            MFSearchResult(schemeCode: "120503", schemeName: "SBI Bluechip Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "119551", schemeName: "ICICI Prudential Bluechip Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "118989", schemeName: "HDFC Top 100 Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "125494", schemeName: "Mirae Asset Large Cap Fund - Direct Plan - Growth"),
            
            // Mid Cap Funds
            MFSearchResult(schemeCode: "120505", schemeName: "SBI Magnum Midcap Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "119597", schemeName: "ICICI Prudential MidCap Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "118835", schemeName: "HDFC Mid-Cap Opportunities Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "119226", schemeName: "Motilal Oswal Midcap Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "125494", schemeName: "Quant Mid Cap Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "118834", schemeName: "Kotak Emerging Equity Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "120716", schemeName: "Nippon India Growth Fund - Direct Plan - Growth"),
            
            // Small Cap Funds
            MFSearchResult(schemeCode: "119598", schemeName: "ICICI Prudential SmallCap Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "118836", schemeName: "HDFC Small Cap Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "120717", schemeName: "Axis Small Cap Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "120716", schemeName: "Quant Small Cap Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "118989", schemeName: "Nippon India Small Cap Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "125497", schemeName: "Kotak Small Cap Fund - Direct Plan - Growth"),
            
            // ELSS Funds
            MFSearchResult(schemeCode: "120504", schemeName: "SBI Long Term Equity Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "119226", schemeName: "Axis Long Term Equity Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "118825", schemeName: "HDFC TaxSaver Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "125495", schemeName: "Mirae Asset Tax Saver Fund - Direct Plan - Growth"),
            
            // Index Funds
            MFSearchResult(schemeCode: "120506", schemeName: "SBI Nifty Index Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "119552", schemeName: "ICICI Prudential Nifty Index Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "118990", schemeName: "HDFC Index Fund - Nifty 50 Plan - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "120716", schemeName: "UTI Nifty Index Fund - Direct Plan - Growth"),
            
            // Hybrid Funds
            MFSearchResult(schemeCode: "118556", schemeName: "HDFC Hybrid Equity Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "119226", schemeName: "ICICI Prudential Equity & Debt Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "120507", schemeName: "SBI Equity Hybrid Fund - Direct Plan - Growth"),
            
            // Sectoral Funds
            MFSearchResult(schemeCode: "118991", schemeName: "HDFC Banking and Financial Services Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "119599", schemeName: "ICICI Prudential Technology Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "120508", schemeName: "SBI Healthcare Opportunities Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "119600", schemeName: "ICICI Prudential Pharma Healthcare Fund - Direct Plan - Growth"),
            
            // International Funds
            MFSearchResult(schemeCode: "125496", schemeName: "Mirae Asset NYSE FANG+ ETF Fund of Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "119601", schemeName: "ICICI Prudential US Bluechip Equity Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "119602", schemeName: "Franklin India Feeder - Franklin US Opportunities Fund - Direct Plan - Growth"),
            
            // Debt Funds
            MFSearchResult(schemeCode: "118992", schemeName: "HDFC Short Term Debt Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "120509", schemeName: "SBI Corporate Bond Fund - Direct Plan - Growth"),
            MFSearchResult(schemeCode: "119603", schemeName: "ICICI Prudential Corporate Bond Fund - Direct Plan - Growth")
        ]
    }
}

// MARK: - Data Models
struct MFSearchResult: Identifiable, Codable {
    let id = UUID()
    let schemeCode: String
    let schemeName: String
    
    enum CodingKeys: String, CodingKey {
        case schemeCode, schemeName
    }
}

// MARK: - API Response Models

// MF List API Response Model
struct MFListItem: Codable {
    let schemeCode: Int
    let schemeName: String
    
    enum CodingKeys: String, CodingKey {
        case schemeCode
        case schemeName
    }
}

// MFAPI Response Models
struct MFAPIResponse: Codable {
    let meta: MFAPIMeta
    let data: [MFAPIData]
}

struct MFAPIMeta: Codable {
    let fundHouse: String
    let schemeType: String
    let schemeCategory: String
    let schemeCode: String
    let schemeName: String
    
    enum CodingKeys: String, CodingKey {
        case fundHouse = "fund_house"
        case schemeType = "scheme_type"
        case schemeCategory = "scheme_category"
        case schemeCode = "scheme_code"
        case schemeName = "scheme_name"
    }
}

struct MFAPIData: Codable {
    let date: String
    let nav: String
}

// Note: Yahoo Finance Response Models are defined in StockPriceService.swift 