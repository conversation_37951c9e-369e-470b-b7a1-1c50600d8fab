import Foundation
import CryptoKit

@MainActor
final class CoinDCXService: ObservableObject {
    static let shared = CoinDCXService()
    
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var lastSyncTime: Date? = nil
    
    private let baseURL = "https://api.coindcx.com"
    private let session = URLSession.shared
    
    private init() {}
    
    // MARK: - Data Models
    struct CoinDCXBalance: Codable {
        let currency: String
        let balance: Double
        let locked_balance: Double

        var availableBalance: Double {
            return balance
        }

        var lockedBalance: Double {
            return locked_balance
        }

        var totalBalance: Double {
            return availableBalance + lockedBalance
        }
    }

    // Alternative response structure in case the API returns different format
    struct CoinDCXBalanceResponse: Codable {
        let balances: [CoinDCXBalance]?
        let data: [CoinDCXBalance]?
        let result: [CoinDCXBalance]?
    }
    
    struct CoinDCXError: Codable {
        let message: String
        let code: Int?
    }
    
    enum CoinDCXServiceError: LocalizedError {
        case invalidCredentials
        case invalidResponse
        case networkError(String)
        case authenticationFailed
        case noBalanceData
        
        var errorDescription: String? {
            switch self {
            case .invalidCredentials:
                return "Invalid API credentials. Please check your API key and secret."
            case .invalidResponse:
                return "Invalid response from CoinDCX API."
            case .networkError(let message):
                return "Network error: \(message)"
            case .authenticationFailed:
                return "Authentication failed. Please verify your API credentials."
            case .noBalanceData:
                return "No balance data received from CoinDCX."
            }
        }
    }
    
    // MARK: - Authentication
    private func createSignature(body: String, secret: String) -> String {
        let key = SymmetricKey(data: Data(secret.utf8))
        let signature = HMAC<SHA256>.authenticationCode(for: Data(body.utf8), using: key)
        return Data(signature).map { String(format: "%02hhx", $0) }.joined()
    }
    
    private func createAuthenticatedRequest(
        endpoint: String,
        body: [String: Any],
        apiKey: String,
        apiSecret: String
    ) throws -> URLRequest {
        guard let url = URL(string: "\(baseURL)\(endpoint)") else {
            throw CoinDCXServiceError.invalidResponse
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "X-AUTH-APIKEY")
        
        let jsonData = try JSONSerialization.data(withJSONObject: body)
        let jsonString = String(data: jsonData, encoding: .utf8) ?? ""

        let signature = createSignature(body: jsonString, secret: apiSecret)
        request.setValue(signature, forHTTPHeaderField: "X-AUTH-SIGNATURE")
        request.httpBody = jsonData

        // Debug logging removed for cleaner output
        
        return request
    }
    
    // MARK: - API Methods
    func fetchPortfolioBalances(apiKey: String, apiSecret: String) async throws -> [CoinDCXBalance] {
        isLoading = true
        errorMessage = nil
        
        defer {
            Task { @MainActor in
                self.isLoading = false
            }
        }
        
        let timestamp = Int(Date().timeIntervalSince1970 * 1000)
        let body: [String: Any] = ["timestamp": timestamp]
        
        do {
            let request = try createAuthenticatedRequest(
                endpoint: "/exchange/v1/users/balances",
                body: body,
                apiKey: apiKey,
                apiSecret: apiSecret
            )
            
            let (data, response) = try await session.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw CoinDCXServiceError.invalidResponse
            }

            // Debug logging removed for cleaner output

            if httpResponse.statusCode == 401 {
                throw CoinDCXServiceError.authenticationFailed
            }

            guard httpResponse.statusCode == 200 else {
                // Try to parse error message
                if let errorData = try? JSONDecoder().decode(CoinDCXError.self, from: data) {
                    throw CoinDCXServiceError.networkError(errorData.message)
                } else {
                    let responseText = String(data: data, encoding: .utf8) ?? "Unknown error"
                    throw CoinDCXServiceError.networkError("HTTP \(httpResponse.statusCode): \(responseText)")
                }
            }

            do {
                // Try to decode as direct array first
                var balances: [CoinDCXBalance] = []

                // Decode the response as direct array (we know this works now)
                balances = try JSONDecoder().decode([CoinDCXBalance].self, from: data)

                // Filter out zero balances and very small amounts
                let nonZeroBalances = balances.filter { balance in
                    balance.totalBalance > 0.000001 // Filter out dust amounts
                }

                await MainActor.run {
                    self.lastSyncTime = Date()
                }

                return nonZeroBalances

            } catch let decodingError {
                print("🔍 JSON Decoding Error: \(decodingError)")
                if let responseString = String(data: data, encoding: .utf8) {
                    print("🔍 Raw Response for debugging: \(responseString)")
                }
                throw CoinDCXServiceError.networkError("Failed to parse response: \(decodingError.localizedDescription)")
            }
            
        } catch let error as CoinDCXServiceError {
            await MainActor.run {
                self.errorMessage = error.errorDescription
            }
            throw error
        } catch {
            let errorMsg = "Failed to fetch portfolio: \(error.localizedDescription)"
            await MainActor.run {
                self.errorMessage = errorMsg
            }
            throw CoinDCXServiceError.networkError(errorMsg)
        }
    }
    
    // MARK: - Portfolio Conversion
    func convertBalancesToCryptos(_ balances: [CoinDCXBalance]) async -> [Crypto] {
        var cryptos: [Crypto] = []

        for balance in balances {
            // Skip fiat currencies
            if balance.currency == "INR" || balance.currency == "USDT" {
                continue
            }

            // Get current price for the crypto
            let currentPrice = await CryptoPriceService.shared.fetchCurrentPrice(for: balance.currency) ?? 0.0

            // Create crypto object using the API-specific initializer
            let crypto = Crypto(
                fromAPI: getCryptoName(for: balance.currency),
                symbol: balance.currency,
                exchange: "CoinDCX",
                availableBalance: balance.availableBalance,
                lockedBalance: balance.lockedBalance,
                currentPrice: currentPrice
            )

            cryptos.append(crypto)
        }

        return cryptos
    }

    private func getCryptoName(for symbol: String) -> String {
        // Map common symbols to full names
        let cryptoNames: [String: String] = [
            "BTC": "Bitcoin",
            "ETH": "Ethereum",
            "BNB": "Binance Coin",
            "ADA": "Cardano",
            "DOT": "Polkadot",
            "LINK": "Chainlink",
            "LTC": "Litecoin",
            "BCH": "Bitcoin Cash",
            "XLM": "Stellar",
            "UNI": "Uniswap",
            "AAVE": "Aave",
            "SUSHI": "SushiSwap",
            "COMP": "Compound",
            "MKR": "Maker",
            "SNX": "Synthetix",
            "YFI": "yearn.finance",
            "CRV": "Curve DAO Token",
            "BAL": "Balancer",
            "MATIC": "Polygon",
            "AVAX": "Avalanche",
            "SOL": "Solana",
            "ALGO": "Algorand",
            "ATOM": "Cosmos",
            "XTZ": "Tezos",
            "FIL": "Filecoin",
            "THETA": "Theta Network",
            "VET": "VeChain",
            "TRX": "TRON",
            "EOS": "EOS",
            "NEO": "Neo",
            "IOTA": "IOTA",
            "XMR": "Monero",
            "DASH": "Dash",
            "ZEC": "Zcash",
            "ETC": "Ethereum Classic"
        ]

        return cryptoNames[symbol] ?? symbol
    }

    // MARK: - Helper Methods
    func validateCredentials(apiKey: String, apiSecret: String) -> Bool {
        return !apiKey.isEmpty && !apiSecret.isEmpty && apiKey.count > 10 && apiSecret.count > 10
    }

    func testConnection(apiKey: String, apiSecret: String) async -> Bool {
        do {
            let balances = try await fetchPortfolioBalances(apiKey: apiKey, apiSecret: apiSecret)
            await MainActor.run {
                self.errorMessage = nil // Clear any previous errors
            }
            return true
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
            return false
        }
    }

    // Debug method to test raw API call
    func debugAPICall(apiKey: String, apiSecret: String) async -> String {
        var debugOutput = "🔍 Starting debug API call...\n"

        let timestamp = Int(Date().timeIntervalSince1970 * 1000)
        let body: [String: Any] = ["timestamp": timestamp]

        do {
            let request = try createAuthenticatedRequest(
                endpoint: "/exchange/v1/users/balances",
                body: body,
                apiKey: apiKey,
                apiSecret: apiSecret
            )

            debugOutput += "🔍 Request created successfully\n"
            debugOutput += "🔍 URL: \(request.url?.absoluteString ?? "nil")\n"
            debugOutput += "🔍 Method: \(request.httpMethod ?? "nil")\n"

            let (data, response) = try await session.data(for: request)

            debugOutput += "🔍 Raw API Response received\n"
            debugOutput += "🔍 Response type: \(type(of: response))\n"

            if let httpResponse = response as? HTTPURLResponse {
                debugOutput += "🔍 HTTP Status: \(httpResponse.statusCode)\n"
                debugOutput += "🔍 HTTP Headers: \(httpResponse.allHeaderFields)\n"
            }

            if let responseString = String(data: data, encoding: .utf8) {
                debugOutput += "🔍 Response body: \(responseString)\n"
                print("🔍 Response body: \(responseString)")
            } else {
                debugOutput += "🔍 Could not convert response to string\n"
            }

        } catch {
            debugOutput += "🔍 Debug API call failed: \(error)\n"
            print("🔍 Debug API call failed: \(error)")
        }

        return debugOutput
    }

    func clearError() {
        errorMessage = nil
    }

    // MARK: - Debug/Testing Methods
    #if DEBUG
    func createTestCrypto() -> [Crypto] {
        // Create some test crypto data for development/testing
        return [
            Crypto(
                fromAPI: "Bitcoin",
                symbol: "BTC",
                exchange: "CoinDCX",
                availableBalance: 0.1,
                lockedBalance: 0.0,
                currentPrice: 3200000.0
            ),
            Crypto(
                fromAPI: "Ethereum",
                symbol: "ETH",
                exchange: "CoinDCX",
                availableBalance: 1.5,
                lockedBalance: 0.2,
                currentPrice: 190000.0
            )
        ]
    }

    func simulateAPIResponse() async -> [CoinDCXBalance] {
        // Simulate API response for testing
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second delay

        return [
            CoinDCXBalance(currency: "BTC", balance: "0.1", locked_balance: "0.0"),
            CoinDCXBalance(currency: "ETH", balance: "1.5", locked_balance: "0.2"),
            CoinDCXBalance(currency: "USDT", balance: "1000.0", locked_balance: "0.0") // This should be filtered out
        ]
    }
    #endif
}
