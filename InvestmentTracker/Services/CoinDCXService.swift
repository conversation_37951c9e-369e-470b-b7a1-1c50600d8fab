import Foundation
import CryptoKit

@MainActor
final class CoinDCXService: ObservableObject {
    static let shared = CoinDCXService()
    
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var lastSyncTime: Date? = nil
    
    private let baseURL = "https://api.coindcx.com"
    private let session = URLSession.shared
    
    private init() {}
    
    // MARK: - Data Models
    struct CoinDCXBalance: Codable {
        let currency: String
        let balance: Double
        let locked_balance: Double

        var availableBalance: Double {
            return balance
        }

        var lockedBalance: Double {
            return locked_balance
        }

        var totalBalance: Double {
            return availableBalance + lockedBalance
        }
    }

    // Alternative response structure in case the API returns different format
    struct CoinDCXBalanceResponse: Codable {
        let balances: [CoinDCXBalance]?
        let data: [CoinDCXBalance]?
        let result: [CoinDCXBalance]?
    }
    
    struct CoinDCXError: Codable {
        let message: String
        let code: Int?
    }

    // Trade history model for calculating average prices (updated to match actual API response)
    struct CoinDCXTrade: Codable {
        let id: Int
        let order_id: String
        let side: String // "buy" or "sell"
        let fee_amount: String
        let ecode: String
        let quantity: String
        let price: String
        let symbol: String
        let timestamp: Double

        var isBuy: Bool {
            return side.lowercased() == "buy"
        }

        var targetQuantity: Double {
            return Double(quantity) ?? 0.0
        }

        var pricePerUnit: Double {
            return Double(price) ?? 0.0
        }

        var feeAmount: Double {
            return Double(fee_amount) ?? 0.0
        }

        var totalCost: Double {
            return targetQuantity * pricePerUnit
        }

        // Extract currency symbol from the symbol field (e.g., "XRPINR" -> "XRP")
        var targetCurrencySymbol: String {
            // Remove common base currencies from the end
            let baseCurrencies = ["INR", "USDT", "BTC", "ETH"]
            for base in baseCurrencies {
                if symbol.hasSuffix(base) {
                    return String(symbol.dropLast(base.count))
                }
            }
            return symbol
        }
    }
    
    enum CoinDCXServiceError: LocalizedError {
        case invalidCredentials
        case invalidResponse
        case networkError(String)
        case authenticationFailed
        case noBalanceData
        
        var errorDescription: String? {
            switch self {
            case .invalidCredentials:
                return "Invalid API credentials. Please check your API key and secret."
            case .invalidResponse:
                return "Invalid response from CoinDCX API."
            case .networkError(let message):
                return "Network error: \(message)"
            case .authenticationFailed:
                return "Authentication failed. Please verify your API credentials."
            case .noBalanceData:
                return "No balance data received from CoinDCX."
            }
        }
    }
    
    // MARK: - Authentication
    private func createSignature(body: String, secret: String) -> String {
        let key = SymmetricKey(data: Data(secret.utf8))
        let signature = HMAC<SHA256>.authenticationCode(for: Data(body.utf8), using: key)
        return Data(signature).map { String(format: "%02hhx", $0) }.joined()
    }
    
    private func createAuthenticatedRequest(
        endpoint: String,
        body: [String: Any],
        apiKey: String,
        apiSecret: String
    ) throws -> URLRequest {
        guard let url = URL(string: "\(baseURL)\(endpoint)") else {
            throw CoinDCXServiceError.invalidResponse
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "X-AUTH-APIKEY")
        
        let jsonData = try JSONSerialization.data(withJSONObject: body)
        let jsonString = String(data: jsonData, encoding: .utf8) ?? ""

        let signature = createSignature(body: jsonString, secret: apiSecret)
        request.setValue(signature, forHTTPHeaderField: "X-AUTH-SIGNATURE")
        request.httpBody = jsonData

        // Debug logging removed for cleaner output
        
        return request
    }
    
    // MARK: - API Methods
    func fetchPortfolioBalances(apiKey: String, apiSecret: String) async throws -> [CoinDCXBalance] {
        isLoading = true
        errorMessage = nil
        
        defer {
            Task { @MainActor in
                self.isLoading = false
            }
        }
        
        let timestamp = Int(Date().timeIntervalSince1970 * 1000)
        let body: [String: Any] = ["timestamp": timestamp]
        
        do {
            let request = try createAuthenticatedRequest(
                endpoint: "/exchange/v1/users/balances",
                body: body,
                apiKey: apiKey,
                apiSecret: apiSecret
            )
            
            let (data, response) = try await session.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw CoinDCXServiceError.invalidResponse
            }

            // Debug logging removed for cleaner output

            if httpResponse.statusCode == 401 {
                throw CoinDCXServiceError.authenticationFailed
            }

            guard httpResponse.statusCode == 200 else {
                // Try to parse error message
                if let errorData = try? JSONDecoder().decode(CoinDCXError.self, from: data) {
                    throw CoinDCXServiceError.networkError(errorData.message)
                } else {
                    let responseText = String(data: data, encoding: .utf8) ?? "Unknown error"
                    throw CoinDCXServiceError.networkError("HTTP \(httpResponse.statusCode): \(responseText)")
                }
            }

            do {
                // Try to decode as direct array first
                var balances: [CoinDCXBalance] = []

                // Decode the response as direct array (we know this works now)
                balances = try JSONDecoder().decode([CoinDCXBalance].self, from: data)

                // Filter out zero balances and very small amounts
                let nonZeroBalances = balances.filter { balance in
                    balance.totalBalance > 0.000001 // Filter out dust amounts
                }

                await MainActor.run {
                    self.lastSyncTime = Date()
                }

                return nonZeroBalances

            } catch let decodingError {
                print("🔍 JSON Decoding Error: \(decodingError)")
                if let responseString = String(data: data, encoding: .utf8) {
                    print("🔍 Raw Response for debugging: \(responseString)")
                }
                throw CoinDCXServiceError.networkError("Failed to parse response: \(decodingError.localizedDescription)")
            }
            
        } catch let error as CoinDCXServiceError {
            await MainActor.run {
                self.errorMessage = error.errorDescription
            }
            throw error
        } catch {
            let errorMsg = "Failed to fetch portfolio: \(error.localizedDescription)"
            await MainActor.run {
                self.errorMessage = errorMsg
            }
            throw CoinDCXServiceError.networkError(errorMsg)
        }
    }

    // MARK: - Trade History API
    func fetchTradeHistory(apiKey: String, apiSecret: String, symbol: String? = nil, limit: Int = 1000) async throws -> [CoinDCXTrade] {
        let timestamp = Int(Date().timeIntervalSince1970 * 1000)
        var body: [String: Any] = [
            "timestamp": timestamp,
            "limit": limit
        ]

        // Add symbol filter if provided
        if let symbol = symbol {
            body["symbol"] = symbol
        }

        do {
            let request = try createAuthenticatedRequest(
                endpoint: "/exchange/v1/orders/trade_history",
                body: body,
                apiKey: apiKey,
                apiSecret: apiSecret
            )

            let (data, response) = try await session.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw CoinDCXServiceError.invalidResponse
            }

            // Debug: Print response details for trade history
            print("🔍 Trade History API Response:")
            print("🔍 Status Code: \(httpResponse.statusCode)")
            print("🔍 Headers: \(httpResponse.allHeaderFields)")

            if let responseString = String(data: data, encoding: .utf8) {
                print("🔍 Trade History Response Body: \(responseString)")
            }

            if httpResponse.statusCode == 401 {
                throw CoinDCXServiceError.authenticationFailed
            }

            guard httpResponse.statusCode == 200 else {
                if let errorData = try? JSONDecoder().decode(CoinDCXError.self, from: data) {
                    throw CoinDCXServiceError.networkError(errorData.message)
                } else {
                    let responseText = String(data: data, encoding: .utf8) ?? "Unknown error"
                    throw CoinDCXServiceError.networkError("HTTP \(httpResponse.statusCode): \(responseText)")
                }
            }

            do {
                let trades = try JSONDecoder().decode([CoinDCXTrade].self, from: data)
                print("🔍 Successfully decoded \(trades.count) trades")
                return trades
            } catch let decodingError {
                print("🔍 Trade History JSON Decoding Error: \(decodingError)")
                if let responseString = String(data: data, encoding: .utf8) {
                    print("🔍 Raw Trade History Response for debugging: \(responseString)")
                }
                throw CoinDCXServiceError.networkError("Failed to parse trade history response: \(decodingError.localizedDescription)")
            }

        } catch let error as CoinDCXServiceError {
            throw error
        } catch {
            throw CoinDCXServiceError.networkError("Failed to fetch trade history: \(error.localizedDescription)")
        }
    }

    // MARK: - Complete Trade History with Pagination
    func fetchAllTradeHistory(apiKey: String, apiSecret: String) async throws -> [CoinDCXTrade] {
        var allTrades: [CoinDCXTrade] = []
        var offset = 0
        let limit = 500
        var hasMoreTrades = true

        print("🔍 Fetching complete trade history with pagination...")

        while hasMoreTrades {
            let timestamp = Int(Date().timeIntervalSince1970 * 1000)
            let body: [String: Any] = [
                "timestamp": timestamp,
                "limit": limit,
                "offset": offset
            ]

            do {
                let request = try createAuthenticatedRequest(
                    endpoint: "/exchange/v1/orders/trade_history",
                    body: body,
                    apiKey: apiKey,
                    apiSecret: apiSecret
                )

                let (data, response) = try await session.data(for: request)

                guard let httpResponse = response as? HTTPURLResponse else {
                    throw CoinDCXServiceError.invalidResponse
                }

                if httpResponse.statusCode == 401 {
                    throw CoinDCXServiceError.authenticationFailed
                }

                guard httpResponse.statusCode == 200 else {
                    if let errorData = try? JSONDecoder().decode(CoinDCXError.self, from: data) {
                        throw CoinDCXServiceError.networkError(errorData.message)
                    } else {
                        let responseText = String(data: data, encoding: .utf8) ?? "Unknown error"
                        throw CoinDCXServiceError.networkError("HTTP \(httpResponse.statusCode): \(responseText)")
                    }
                }

                let trades = try JSONDecoder().decode([CoinDCXTrade].self, from: data)
                print("🔍 Fetched \(trades.count) trades at offset \(offset)")

                // Debug: Print response to see if offset is supported
                if let responseString = String(data: data, encoding: .utf8) {
                    print("🔍 Response length: \(responseString.count) characters")
                }

                if trades.isEmpty || trades.count < limit {
                    print("🔍 No more trades available (got \(trades.count), expected \(limit))")
                    hasMoreTrades = false
                } else {
                    print("🔍 More trades might be available, continuing...")
                }

                allTrades.append(contentsOf: trades)
                offset += limit

                // Add small delay to avoid rate limiting
                try await Task.sleep(nanoseconds: 100_000_000) // 0.1 second

            } catch let error as CoinDCXServiceError {
                throw error
            } catch {
                throw CoinDCXServiceError.networkError("Failed to fetch trade history: \(error.localizedDescription)")
            }
        }

        print("🔍 Total trades fetched: \(allTrades.count)")
        return allTrades
    }

    // MARK: - Portfolio Conversion
    func convertBalancesToCryptos(_ balances: [CoinDCXBalance], apiKey: String, apiSecret: String) async -> [Crypto] {
        var cryptos: [Crypto] = []

        for balance in balances {
            // Skip fiat currencies
            if balance.currency == "INR" || balance.currency == "USDT" {
                continue
            }

            // Get current price for the crypto
            let currentPrice = await CryptoPriceService.shared.fetchCurrentPrice(for: balance.currency) ?? 0.0

            // Calculate average purchase price from trade history
            print("🔍 Processing \(balance.currency) with balance: \(balance.totalBalance)")
            let averagePrice = await calculateAveragePurchasePrice(
                for: balance.currency,
                currentQuantity: balance.totalBalance,
                apiKey: apiKey,
                apiSecret: apiSecret
            )

            // Use average price if available, otherwise fall back to current price
            let purchasePrice = averagePrice > 0 ? averagePrice : currentPrice
            print("🔍 \(balance.currency): Average price = ₹\(averagePrice), Current price = ₹\(currentPrice), Using = ₹\(purchasePrice)")

            // Create crypto object using the API-specific initializer
            var crypto = Crypto(
                fromAPI: getCryptoName(for: balance.currency),
                symbol: balance.currency,
                exchange: "CoinDCX",
                availableBalance: balance.availableBalance,
                lockedBalance: balance.lockedBalance,
                currentPrice: currentPrice
            )

            // Update the purchase price with our calculated average
            crypto.purchasePrice = purchasePrice
            print("🔍 Final crypto object for \(balance.currency): Purchase = ₹\(crypto.purchasePrice), Current = ₹\(crypto.currentPrice), P&L = ₹\(crypto.currentValue - crypto.investedAmount)")

            cryptos.append(crypto)
        }

        return cryptos
    }

    // MARK: - Average Price Calculation with Proper FIFO Accounting
    private func calculateAveragePurchasePrice(for symbol: String, currentQuantity: Double, apiKey: String, apiSecret: String) async -> Double {
        print("🔍 Calculating average price for \(symbol) with current quantity: \(currentQuantity)")

        do {
            // Fetch ALL trade history for this symbol
            print("🔍 Fetching complete trade history for \(symbol)...")
            let trades = try await fetchAllTradeHistory(apiKey: apiKey, apiSecret: apiSecret)
            print("🔍 Fetched \(trades.count) total trades")

            // Filter trades for this symbol and sort by timestamp
            let symbolTrades = trades.filter { $0.targetCurrencySymbol == symbol }
                .sorted { $0.timestamp < $1.timestamp }

            let buyTrades = symbolTrades.filter { $0.isBuy }
            let sellTrades = symbolTrades.filter { !$0.isBuy }

            print("🔍 Found \(buyTrades.count) buy trades and \(sellTrades.count) sell trades for \(symbol)")

            guard !buyTrades.isEmpty else {
                print("❌ No buy trades found for \(symbol)")
                return 0.0
            }

            // Calculate remaining holdings using FIFO accounting
            return calculateFIFOAveragePrice(
                buyTrades: buyTrades,
                sellTrades: sellTrades,
                currentQuantity: currentQuantity,
                symbol: symbol
            )

        } catch {
            print("❌ Failed to calculate average price for \(symbol): \(error)")
            return 0.0
        }
    }

    private func calculateFIFOAveragePrice(buyTrades: [CoinDCXTrade], sellTrades: [CoinDCXTrade], currentQuantity: Double, symbol: String) -> Double {
        print("🔍 Using FIFO accounting for \(symbol)")

        // Create a queue of buy lots (quantity, price)
        var buyLots: [(quantity: Double, price: Double)] = []
        for trade in buyTrades {
            buyLots.append((quantity: trade.targetQuantity, price: trade.pricePerUnit))
            print("🔍   Buy lot: \(trade.targetQuantity) @ ₹\(trade.pricePerUnit)")
        }

        // Process sell trades to remove sold quantities (FIFO)
        var totalSold: Double = 0
        for sellTrade in sellTrades {
            var remainingToSell = sellTrade.targetQuantity
            totalSold += remainingToSell
            print("🔍   Processing sell: \(remainingToSell) @ ₹\(sellTrade.pricePerUnit)")

            while remainingToSell > 0 && !buyLots.isEmpty {
                if buyLots[0].quantity <= remainingToSell {
                    // Sell entire first lot
                    let soldFromLot = buyLots[0].quantity
                    remainingToSell -= soldFromLot
                    print("🔍     Sold entire lot: \(soldFromLot) @ ₹\(buyLots[0].price)")
                    buyLots.removeFirst()
                } else {
                    // Partially sell first lot
                    buyLots[0].quantity -= remainingToSell
                    print("🔍     Partially sold from lot: \(remainingToSell), remaining: \(buyLots[0].quantity) @ ₹\(buyLots[0].price)")
                    remainingToSell = 0
                }
            }
        }

        // Calculate remaining quantity from buy lots
        let calculatedRemaining = buyLots.reduce(0) { $0 + $1.quantity }
        print("🔍 Calculated remaining from trades: \(calculatedRemaining), Current portfolio: \(currentQuantity)")

        // Use the minimum of calculated remaining and current quantity
        // (in case there are trades not captured in history)
        let quantityToUse = min(calculatedRemaining, currentQuantity)

        if quantityToUse <= 0 {
            print("❌ No remaining quantity to calculate average for \(symbol)")
            return 0.0
        }

        // Calculate weighted average of remaining lots
        var totalCost: Double = 0
        var totalQuantity: Double = 0
        var remainingNeeded = quantityToUse

        for lot in buyLots {
            if remainingNeeded <= 0 { break }

            let quantityFromLot = min(lot.quantity, remainingNeeded)
            totalCost += quantityFromLot * lot.price
            totalQuantity += quantityFromLot
            remainingNeeded -= quantityFromLot

            print("🔍   Using \(quantityFromLot) from lot @ ₹\(lot.price)")
        }

        guard totalQuantity > 0 else {
            print("❌ No quantity available for average calculation")
            return 0.0
        }

        let averagePrice = totalCost / totalQuantity

        // Calculate alternative prices for comparison
        let averagePriceWithFees = calculateAveragePriceWithFees(buyTrades: buyTrades, sellTrades: sellTrades, currentQuantity: currentQuantity, symbol: symbol)
        let weightedAverageAllBuys = buyTrades.reduce(0) { $0 + ($1.targetQuantity * $1.pricePerUnit) } / buyTrades.reduce(0) { $0 + $1.targetQuantity }

        print("✅ FIFO Average price for \(symbol): ₹\(averagePrice) (cost: ₹\(totalCost), qty: \(totalQuantity))")
        print("🔍 Alternative calculations:")
        print("   - With fees included: ₹\(averagePriceWithFees)")
        print("   - Simple weighted average (all buys): ₹\(weightedAverageAllBuys)")
        print("   - CoinDCX shows: ₹247.41 (for comparison)")
        print("📊 Total bought: \(buyTrades.reduce(0) { $0 + $1.targetQuantity }), Total sold: \(totalSold), Remaining: \(calculatedRemaining)")

        return averagePrice
    }

    private func calculateAveragePriceWithFees(buyTrades: [CoinDCXTrade], sellTrades: [CoinDCXTrade], currentQuantity: Double, symbol: String) -> Double {
        // Similar FIFO logic but include fees in the cost basis
        var buyLots: [(quantity: Double, priceWithFees: Double)] = []

        for trade in buyTrades {
            // Calculate effective price including fees
            let totalCost = trade.targetQuantity * trade.pricePerUnit
            let totalCostWithFees = totalCost + trade.feeAmount
            let priceWithFees = totalCostWithFees / trade.targetQuantity

            buyLots.append((quantity: trade.targetQuantity, priceWithFees: priceWithFees))
            print("🔍   Buy lot with fees: \(trade.targetQuantity) @ ₹\(trade.pricePerUnit) + ₹\(trade.feeAmount) fee = ₹\(priceWithFees) effective")
        }

        // Process sells (same FIFO logic)
        for sellTrade in sellTrades {
            var remainingToSell = sellTrade.targetQuantity

            while remainingToSell > 0 && !buyLots.isEmpty {
                if buyLots[0].quantity <= remainingToSell {
                    remainingToSell -= buyLots[0].quantity
                    buyLots.removeFirst()
                } else {
                    buyLots[0].quantity -= remainingToSell
                    remainingToSell = 0
                }
            }
        }

        // Calculate average with fees
        let quantityToUse = min(buyLots.reduce(0) { $0 + $1.quantity }, currentQuantity)
        var totalCostWithFees: Double = 0
        var totalQuantity: Double = 0
        var remainingNeeded = quantityToUse

        for lot in buyLots {
            if remainingNeeded <= 0 { break }

            let quantityFromLot = min(lot.quantity, remainingNeeded)
            totalCostWithFees += quantityFromLot * lot.priceWithFees
            totalQuantity += quantityFromLot
            remainingNeeded -= quantityFromLot
        }

        return totalQuantity > 0 ? totalCostWithFees / totalQuantity : 0.0
    }

    private func getCryptoName(for symbol: String) -> String {
        // Map common symbols to full names
        let cryptoNames: [String: String] = [
            "BTC": "Bitcoin",
            "ETH": "Ethereum",
            "BNB": "Binance Coin",
            "ADA": "Cardano",
            "DOT": "Polkadot",
            "LINK": "Chainlink",
            "LTC": "Litecoin",
            "BCH": "Bitcoin Cash",
            "XLM": "Stellar",
            "UNI": "Uniswap",
            "AAVE": "Aave",
            "SUSHI": "SushiSwap",
            "COMP": "Compound",
            "MKR": "Maker",
            "SNX": "Synthetix",
            "YFI": "yearn.finance",
            "CRV": "Curve DAO Token",
            "BAL": "Balancer",
            "MATIC": "Polygon",
            "AVAX": "Avalanche",
            "SOL": "Solana",
            "ALGO": "Algorand",
            "ATOM": "Cosmos",
            "XTZ": "Tezos",
            "FIL": "Filecoin",
            "THETA": "Theta Network",
            "VET": "VeChain",
            "TRX": "TRON",
            "EOS": "EOS",
            "NEO": "Neo",
            "IOTA": "IOTA",
            "XMR": "Monero",
            "DASH": "Dash",
            "ZEC": "Zcash",
            "ETC": "Ethereum Classic"
        ]

        return cryptoNames[symbol] ?? symbol
    }

    // MARK: - Helper Methods
    func validateCredentials(apiKey: String, apiSecret: String) -> Bool {
        return !apiKey.isEmpty && !apiSecret.isEmpty && apiKey.count > 10 && apiSecret.count > 10
    }

    func testConnection(apiKey: String, apiSecret: String) async -> Bool {
        do {
            _ = try await fetchPortfolioBalances(apiKey: apiKey, apiSecret: apiSecret)
            await MainActor.run {
                self.errorMessage = nil // Clear any previous errors
            }
            return true
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
            return false
        }
    }

    // Debug method to test trade history API
    func debugTradeHistoryCall(apiKey: String, apiSecret: String) async -> String {
        var debugOutput = "🔍 Testing Trade History API...\n"

        let timestamp = Int(Date().timeIntervalSince1970 * 1000)
        let body: [String: Any] = [
            "timestamp": timestamp,
            "limit": 10
        ]

        do {
            let request = try createAuthenticatedRequest(
                endpoint: "/exchange/v1/orders/trade_history",
                body: body,
                apiKey: apiKey,
                apiSecret: apiSecret
            )

            debugOutput += "🔍 Request created successfully\n"
            debugOutput += "🔍 URL: \(request.url?.absoluteString ?? "nil")\n"

            let (data, response) = try await session.data(for: request)

            if let httpResponse = response as? HTTPURLResponse {
                debugOutput += "🔍 HTTP Status: \(httpResponse.statusCode)\n"
            }

            if let responseString = String(data: data, encoding: .utf8) {
                debugOutput += "🔍 Response: \(responseString)\n"
                print("🔍 Trade History Response: \(responseString)")
            }

        } catch {
            debugOutput += "🔍 Error: \(error)\n"
        }

        return debugOutput
    }

    // Debug method to test raw API call
    func debugAPICall(apiKey: String, apiSecret: String) async -> String {
        var debugOutput = "🔍 Starting debug API call...\n"

        let timestamp = Int(Date().timeIntervalSince1970 * 1000)
        let body: [String: Any] = ["timestamp": timestamp]

        do {
            let request = try createAuthenticatedRequest(
                endpoint: "/exchange/v1/users/balances",
                body: body,
                apiKey: apiKey,
                apiSecret: apiSecret
            )

            debugOutput += "🔍 Request created successfully\n"
            debugOutput += "🔍 URL: \(request.url?.absoluteString ?? "nil")\n"
            debugOutput += "🔍 Method: \(request.httpMethod ?? "nil")\n"

            let (data, response) = try await session.data(for: request)

            debugOutput += "🔍 Raw API Response received\n"
            debugOutput += "🔍 Response type: \(type(of: response))\n"

            if let httpResponse = response as? HTTPURLResponse {
                debugOutput += "🔍 HTTP Status: \(httpResponse.statusCode)\n"
                debugOutput += "🔍 HTTP Headers: \(httpResponse.allHeaderFields)\n"
            }

            if let responseString = String(data: data, encoding: .utf8) {
                debugOutput += "🔍 Response body: \(responseString)\n"
                print("🔍 Response body: \(responseString)")
            } else {
                debugOutput += "🔍 Could not convert response to string\n"
            }

        } catch {
            debugOutput += "🔍 Debug API call failed: \(error)\n"
            print("🔍 Debug API call failed: \(error)")
        }

        return debugOutput
    }

    func clearError() {
        errorMessage = nil
    }

    // MARK: - Debug/Testing Methods
    #if DEBUG
    func createTestCrypto() -> [Crypto] {
        // Create some test crypto data for development/testing
        return [
            Crypto(
                fromAPI: "Bitcoin",
                symbol: "BTC",
                exchange: "CoinDCX",
                availableBalance: 0.1,
                lockedBalance: 0.0,
                currentPrice: 3200000.0
            ),
            Crypto(
                fromAPI: "Ethereum",
                symbol: "ETH",
                exchange: "CoinDCX",
                availableBalance: 1.5,
                lockedBalance: 0.2,
                currentPrice: 190000.0
            )
        ]
    }

    func simulateAPIResponse() async -> [CoinDCXBalance] {
        // Simulate API response for testing
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second delay

        return [
            CoinDCXBalance(currency: "BTC", balance: 0.1, locked_balance: 0.0),
            CoinDCXBalance(currency: "ETH", balance: 1.5, locked_balance: 0.2),
            CoinDCXBalance(currency: "USDT", balance: 1000.0, locked_balance: 0.0) // This should be filtered out
        ]
    }
    #endif
}
