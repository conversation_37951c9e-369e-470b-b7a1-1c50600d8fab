import Foundation

@MainActor
class StockPriceService: ObservableObject {
    static let shared = StockPriceService()
    
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var lastUpdated: Date = Date()
    
    // Free APIs for Indian stock prices
    private let yahooFinanceAPI = "https://query1.finance.yahoo.com/v8/finance/chart/"
    private let fallbackAPI = "https://latest.currency-api.pages.dev/v1/currencies/inr.json"
    
    private init() {}
    
    // MARK: - Public Methods
    
    /// Fetch current stock price for a given symbol
    func fetchStockPrice(symbol: String, exchange: String = "NSE") async -> Double? {
        isLoading = true
        errorMessage = nil
        
        do {
            let price = try await fetchFromYahooFinance(symbol: symbol, exchange: exchange)
            lastUpdated = Date()
            isLoading = false
            return price
        } catch {
            print("StockPriceService: Failed to fetch stock price: \(error)")
            errorMessage = "Failed to fetch stock price: \(error.localizedDescription)"
            isLoading = false
            return nil
        }
    }
    
    /// Fetch multiple stock prices at once
    func fetchMultipleStockPrices(symbols: [(symbol: String, exchange: String)]) async -> [String: Double] {
        isLoading = true
        errorMessage = nil
        
        var results: [String: Double] = [:]
        
        // Fetch prices concurrently
        await withTaskGroup(of: (String, Double?).self) { group in
            for (symbol, exchange) in symbols {
                group.addTask {
                    do {
                        let price = try await self.fetchFromYahooFinance(symbol: symbol, exchange: exchange)
                        return (symbol, price)
                    } catch {
                        print("StockPriceService: Failed to fetch price for \(symbol): \(error)")
                        return (symbol, nil)
                    }
                }
            }
            
            for await (symbol, price) in group {
                if let price = price {
                    results[symbol] = price
                }
            }
        }
        
        lastUpdated = Date()
        isLoading = false
        return results
    }
    
    // MARK: - Private Methods
    
    private func fetchFromYahooFinance(symbol: String, exchange: String) async throws -> Double? {
        // Convert Indian stock symbols to Yahoo Finance format
        let yahooSymbol = convertToYahooSymbol(symbol: symbol, exchange: exchange)
        let urlString = "\(yahooFinanceAPI)\(yahooSymbol)"
        
        guard let url = URL(string: urlString) else {
            throw StockPriceError.invalidURL
        }
        
        let (data, response) = try await URLSession.shared.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw StockPriceError.invalidResponse
        }
        
        let stockData = try JSONDecoder().decode(YahooFinanceResponse.self, from: data)
        
        guard let chart = stockData.chart.result.first,
              let meta = chart.meta,
              let currentPrice = meta.regularMarketPrice else {
            throw StockPriceError.noData
        }
        
        return currentPrice
    }
    
    private func convertToYahooSymbol(symbol: String, exchange: String) -> String {
        // Convert Indian stock symbols to Yahoo Finance format
        switch exchange.uppercased() {
        case "NSE":
            return "\(symbol).NS"
        case "BSE":
            return "\(symbol).BO"
        default:
            return "\(symbol).NS" // Default to NSE
        }
    }
    
    /// Get popular Indian stock symbols for quick access
    func getPopularStocks() -> [(symbol: String, name: String, exchange: String)] {
        return [
            ("RELIANCE", "Reliance Industries Ltd", "NSE"),
            ("TCS", "Tata Consultancy Services", "NSE"),
            ("HDFCBANK", "HDFC Bank Ltd", "NSE"),
            ("INFY", "Infosys Ltd", "NSE"),
            ("ICICIBANK", "ICICI Bank Ltd", "NSE"),
            ("HINDUNILVR", "Hindustan Unilever Ltd", "NSE"),
            ("ITC", "ITC Ltd", "NSE"),
            ("SBIN", "State Bank of India", "NSE"),
            ("BHARTIARTL", "Bharti Airtel Ltd", "NSE"),
            ("KOTAKBANK", "Kotak Mahindra Bank", "NSE"),
            ("LT", "Larsen & Toubro Ltd", "NSE"),
            ("AXISBANK", "Axis Bank Ltd", "NSE"),
            ("MARUTI", "Maruti Suzuki India Ltd", "NSE"),
            ("ASIANPAINT", "Asian Paints Ltd", "NSE"),
            ("HCLTECH", "HCL Technologies Ltd", "NSE"),
            ("WIPRO", "Wipro Ltd", "NSE"),
            ("ULTRACEMCO", "UltraTech Cement Ltd", "NSE"),
            ("ONGC", "Oil & Natural Gas Corp", "NSE"),
            ("TATAMOTORS", "Tata Motors Ltd", "NSE"),
            ("POWERGRID", "Power Grid Corp of India", "NSE")
        ]
    }
}

// MARK: - Error Types
enum StockPriceError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case noData
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL for stock price API"
        case .invalidResponse:
            return "Invalid response from stock price API"
        case .noData:
            return "No stock price data available"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}

// MARK: - Yahoo Finance Response Models
struct YahooFinanceResponse: Codable {
    let chart: YahooChart
}

struct YahooChart: Codable {
    let result: [YahooResult]
    let error: String?
}

struct YahooResult: Codable {
    let meta: YahooMeta?
    let timestamp: [Int]?
    let indicators: YahooIndicators?
}

struct YahooMeta: Codable {
    let currency: String?
    let symbol: String?
    let exchangeName: String?
    let instrumentType: String?
    let firstTradeDate: Int?
    let regularMarketTime: Int?
    let gmtoffset: Int?
    let timezone: String?
    let exchangeTimezoneName: String?
    let regularMarketPrice: Double?
    let chartPreviousClose: Double?
    let previousClose: Double?
    let scale: Int?
    let priceHint: Int?
    let currentTradingPeriod: YahooTradingPeriod?
    let tradingPeriods: [[YahooTradingPeriod]]?
    let dataGranularity: String?
    let range: String?
    let validRanges: [String]?
}

struct YahooTradingPeriod: Codable {
    let timezone: String?
    let start: Int?
    let end: Int?
    let gmtoffset: Int?
}

struct YahooIndicators: Codable {
    let quote: [YahooQuote]?
    let adjclose: [YahooAdjClose]?
}

struct YahooQuote: Codable {
    let open: [Double?]?
    let low: [Double?]?
    let high: [Double?]?
    let close: [Double?]?
    let volume: [Int?]?
}

struct YahooAdjClose: Codable {
    let adjclose: [Double?]?
} 