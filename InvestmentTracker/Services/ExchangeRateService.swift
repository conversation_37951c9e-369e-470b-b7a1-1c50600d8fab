import Foundation

@MainActor
class ExchangeRateService: ObservableObject {
    static let shared = ExchangeRateService()
    
    @Published var currentUSDToINRRate: Double = 83.50 // Default fallback rate
    @Published var lastUpdated: Date = Date()
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    
    private let openAPIURL = "https://open.er-api.com/v6/latest/USD"
    private let fallbackAPIURL = "https://latest.currency-api.pages.dev/v1/currencies/usd.json"
    
    private init() {}
    
    // MARK: - Public Methods
    
    func fetchLatestExchangeRate() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let rate = try await fetchFromPrimaryAPI()
            currentUSDToINRRate = rate
            lastUpdated = Date()
            print("✅ Exchange rate updated: 1 USD = ₹\(String(format: "%.2f", rate))")
        } catch {
            print("❌ Primary API failed, trying fallback...")
            do {
                let rate = try await fetchFromFallbackAPI()
                currentUSDToINRRate = rate
                lastUpdated = Date()
                print("✅ Exchange rate updated (fallback): 1 USD = ₹\(String(format: "%.2f", rate))")
            } catch {
                print("❌ All APIs failed: \(error.localizedDescription)")
                errorMessage = "Failed to fetch exchange rate. Using cached rate."
            }
        }
        
        isLoading = false
    }
    
    func fetchLatestExchangeRateSync() {
        Task {
            await fetchLatestExchangeRate()
        }
    }
    
    // MARK: - Private Methods
    
    private func fetchFromPrimaryAPI() async throws -> Double {
        guard let url = URL(string: openAPIURL) else {
            throw ExchangeRateError.invalidURL
        }
        
        let (data, response) = try await URLSession.shared.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw ExchangeRateError.invalidResponse
        }
        
        let exchangeRateResponse = try JSONDecoder().decode(ExchangeRateResponse.self, from: data)
        
        guard let inrRate = exchangeRateResponse.rates["INR"] else {
            throw ExchangeRateError.currencyNotFound
        }
        
        return inrRate
    }
    
    private func fetchFromFallbackAPI() async throws -> Double {
        guard let url = URL(string: fallbackAPIURL) else {
            throw ExchangeRateError.invalidURL
        }
        
        let (data, response) = try await URLSession.shared.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw ExchangeRateError.invalidResponse
        }
        
        let fallbackResponse = try JSONDecoder().decode(FallbackExchangeRateResponse.self, from: data)
        
        guard let inrRate = fallbackResponse.usd["inr"] else {
            throw ExchangeRateError.currencyNotFound
        }
        
        return inrRate
    }
}

// MARK: - Response Models

struct ExchangeRateResponse: Codable {
    let result: String
    let timeLastUpdateUnix: Int
    let timeLastUpdateUTC: String
    let timeNextUpdateUnix: Int
    let timeNextUpdateUTC: String
    let baseCode: String
    let rates: [String: Double]
    
    enum CodingKeys: String, CodingKey {
        case result
        case timeLastUpdateUnix = "time_last_update_unix"
        case timeLastUpdateUTC = "time_last_update_utc"
        case timeNextUpdateUnix = "time_next_update_unix"
        case timeNextUpdateUTC = "time_next_update_utc"
        case baseCode = "base_code"
        case rates
    }
}

struct FallbackExchangeRateResponse: Codable {
    let date: String
    let usd: [String: Double]
}

// MARK: - Error Types

enum ExchangeRateError: LocalizedError {
    case invalidURL
    case invalidResponse
    case currencyNotFound
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid API URL"
        case .invalidResponse:
            return "Invalid response from server"
        case .currencyNotFound:
            return "INR currency not found in response"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
} 