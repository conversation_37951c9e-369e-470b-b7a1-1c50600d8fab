import Foundation

struct CurrencyFormatter {
    static let shared = CurrencyFormatter()
    
    private let formatter: NumberFormatter
    
    private init() {
        formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "INR"
        formatter.currencySymbol = "₹"
        formatter.maximumFractionDigits = 2
        formatter.minimumFractionDigits = 0
    }
    
    func format(_ amount: Double) -> String {
        return formatter.string(from: NSNumber(value: amount)) ?? "₹0"
    }
    
    func formatWithoutSymbol(_ amount: Double) -> String {
        let tempFormatter = NumberFormatter()
        tempFormatter.numberStyle = .decimal
        tempFormatter.maximumFractionDigits = 2
        tempFormatter.minimumFractionDigits = 0
        return tempFormatter.string(from: NSNumber(value: amount)) ?? "0"
    }
    
    func formatLargeAmount(_ amount: Double) -> String {
        let absAmount = abs(amount)
        
        if absAmount >= 10_000_000 { // 1 Crore
            let crores = amount / 10_000_000
            return String(format: "₹%.2fCr", crores)
        } else if absAmount >= 100_000 { // 1 Lakh
            let lakhs = amount / 100_000
            return String(format: "₹%.2fL", lakhs)
        } else if absAmount >= 1000 { // 1 Thousand
            let thousands = amount / 1000
            return String(format: "₹%.1fK", thousands)
        } else {
            return format(amount)
        }
    }
    
    func formatCryptoPrice(_ amount: Double) -> String {
        let absAmount = abs(amount)
        
        // For very small amounts (like PEPE), use more decimal places
        if absAmount < 0.01 {
            return String(format: "₹%.8f", amount)
        } else if absAmount < 1 {
            return String(format: "₹%.6f", amount)
        } else if absAmount < 100 {
            return String(format: "₹%.4f", amount)
        } else {
            return format(amount)
        }
    }
}

// MARK: - Global Helper Function
func formatCurrency(_ amount: Double) -> String {
    return CurrencyFormatter.shared.format(amount)
}

func formatLargeCurrency(_ amount: Double) -> String {
    return CurrencyFormatter.shared.formatLargeAmount(amount)
}

func formatCryptoPrice(_ amount: Double) -> String {
    return CurrencyFormatter.shared.formatCryptoPrice(amount)
} 