import Foundation

// MARK: - Percentage Calculator
struct PercentageCalculator {
    static let shared = PercentageCalculator()
    
    private init() {}
    
    // MARK: - Basic Percentage Calculations
    
    /// Calculate percentage change between two values
    /// - Parameters:
    ///   - oldValue: Original value
    ///   - newValue: New value
    /// - Returns: Percentage change (positive for increase, negative for decrease)
    func percentageChange(from oldValue: Double, to newValue: Double) -> Double {
        guard oldValue != 0 else { return newValue > 0 ? 100 : 0 }
        return ((newValue - oldValue) / oldValue) * 100
    }
    
    /// Calculate what percentage one value is of another
    /// - Parameters:
    ///   - value: The value to calculate percentage for
    ///   - total: The total value
    /// - Returns: Percentage that value represents of total
    func percentageOf(value: Double, total: Double) -> Double {
        guard total != 0 else { return 0 }
        return (value / total) * 100
    }
    
    /// Calculate value from percentage
    /// - Parameters:
    ///   - percentage: Percentage (0-100)
    ///   - total: Total value
    /// - Returns: Calculated value
    func valueFromPercentage(_ percentage: Double, of total: Double) -> Double {
        return (percentage / 100) * total
    }
    
    // MARK: - Investment Specific Calculations
    
    /// Calculate simple interest
    /// - Parameters:
    ///   - principal: Principal amount
    ///   - rate: Annual interest rate (as percentage)
    ///   - time: Time period in years
    /// - Returns: Simple interest amount
    func calculateSimpleInterest(principal: Double, rate: Double, time: Double) -> Double {
        return (principal * rate * time) / 100
    }
    
    /// Calculate compound interest
    /// - Parameters:
    ///   - principal: Principal amount
    ///   - rate: Annual interest rate (as percentage)
    ///   - time: Time period in years
    ///   - compoundingFrequency: How many times per year interest compounds (default: 1)
    /// - Returns: Compound interest amount
    func calculateCompoundInterest(principal: Double, rate: Double, time: Double, compoundingFrequency: Double = 1) -> Double {
        let amount = principal * pow((1 + (rate / (100 * compoundingFrequency))), (compoundingFrequency * time))
        return amount - principal
    }
    
    /// Calculate final amount with compound interest
    /// - Parameters:
    ///   - principal: Principal amount
    ///   - rate: Annual interest rate (as percentage)
    ///   - time: Time period in years
    ///   - compoundingFrequency: How many times per year interest compounds (default: 1)
    /// - Returns: Final amount after compound interest
    func calculateCompoundAmount(principal: Double, rate: Double, time: Double, compoundingFrequency: Double = 1) -> Double {
        return principal * pow((1 + (rate / (100 * compoundingFrequency))), (compoundingFrequency * time))
    }
    
    // MARK: - Portfolio Calculations
    
    /// Calculate portfolio weight for an asset
    /// - Parameters:
    ///   - assetValue: Value of the specific asset
    ///   - portfolioValue: Total portfolio value
    /// - Returns: Weight as percentage (0-100)
    func calculatePortfolioWeight(assetValue: Double, portfolioValue: Double) -> Double {
        return percentageOf(value: assetValue, total: portfolioValue)
    }
    
    /// Calculate required return for target amount
    /// - Parameters:
    ///   - currentValue: Current investment value
    ///   - targetValue: Target value to achieve
    ///   - years: Number of years to achieve target
    /// - Returns: Required annual return percentage
    func calculateRequiredReturn(currentValue: Double, targetValue: Double, years: Double) -> Double? {
        guard currentValue > 0, years > 0 else { return nil }
        
        let requiredReturn = pow(targetValue / currentValue, 1.0 / years) - 1.0
        return requiredReturn * 100
    }
    
    // MARK: - Risk Calculations
    
    /// Calculate drawdown percentage
    /// - Parameters:
    ///   - peakValue: Peak value before drawdown
    ///   - troughValue: Lowest value during drawdown
    /// - Returns: Drawdown percentage (always positive)
    func calculateDrawdown(peakValue: Double, troughValue: Double) -> Double {
        guard peakValue > 0 else { return 0 }
        return ((peakValue - troughValue) / peakValue) * 100
    }
    
    /// Calculate recovery percentage needed
    /// - Parameter drawdownPercentage: The drawdown percentage
    /// - Returns: Percentage gain needed to recover from drawdown
    func calculateRecoveryNeeded(fromDrawdown drawdownPercentage: Double) -> Double {
        guard drawdownPercentage > 0 else { return 0 }
        return (100 / (100 - drawdownPercentage)) * 100 - 100
    }
    
    // MARK: - Formatting Helpers
    
    /// Format percentage with appropriate decimal places
    /// - Parameters:
    ///   - percentage: Percentage value
    ///   - decimalPlaces: Number of decimal places (default: 2)
    ///   - includeSymbol: Whether to include % symbol (default: true)
    /// - Returns: Formatted percentage string
    func formatPercentage(_ percentage: Double, decimalPlaces: Int = 2, includeSymbol: Bool = true) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.minimumFractionDigits = 0
        formatter.maximumFractionDigits = decimalPlaces
        
        let formattedNumber = formatter.string(from: NSNumber(value: percentage)) ?? "0"
        return includeSymbol ? "\(formattedNumber)%" : formattedNumber
    }
    
    /// Format percentage with color indication
    /// - Parameters:
    ///   - percentage: Percentage value
    ///   - decimalPlaces: Number of decimal places (default: 2)
    /// - Returns: Tuple with formatted string and color indication
    func formatPercentageWithColor(_ percentage: Double, decimalPlaces: Int = 2) -> (text: String, isPositive: Bool) {
        let formattedText = formatPercentage(percentage, decimalPlaces: decimalPlaces)
        return (formattedText, percentage >= 0)
    }
}

// MARK: - Global Helper Functions
func percentageChange(from oldValue: Double, to newValue: Double) -> Double {
    return PercentageCalculator.shared.percentageChange(from: oldValue, to: newValue)
}

func percentageOf(value: Double, total: Double) -> Double {
    return PercentageCalculator.shared.percentageOf(value: value, total: total)
}

func formatPercentage(_ percentage: Double, decimalPlaces: Int = 2) -> String {
    return PercentageCalculator.shared.formatPercentage(percentage, decimalPlaces: decimalPlaces)
}

func calculateSimpleInterest(principal: Double, rate: Double, time: Double) -> Double {
    return PercentageCalculator.shared.calculateSimpleInterest(principal: principal, rate: rate, time: time)
}

func calculateCompoundInterest(principal: Double, rate: Double, time: Double, compoundingFrequency: Double = 1) -> Double {
    return PercentageCalculator.shared.calculateCompoundInterest(principal: principal, rate: rate, time: time, compoundingFrequency: compoundingFrequency)
} 