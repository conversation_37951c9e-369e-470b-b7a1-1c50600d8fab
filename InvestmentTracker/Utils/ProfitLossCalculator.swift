import Foundation

// MARK: - Profit & Loss Calculator
struct ProfitLossCalculator {
    static let shared = ProfitLossCalculator()
    
    private init() {}
    
    // MARK: - Basic P&L Calculations
    
    /// Calculate absolute profit or loss
    /// - Parameters:
    ///   - investedAmount: Total amount invested
    ///   - currentValue: Current market value
    /// - Returns: Profit (positive) or Loss (negative) amount
    func calculateAbsolutePL(investedAmount: Double, currentValue: Double) -> Double {
        return currentValue - investedAmount
    }
    
    /// Calculate percentage profit or loss
    /// - Parameters:
    ///   - investedAmount: Total amount invested
    ///   - currentValue: Current market value
    /// - Returns: Profit/Loss percentage
    func calculatePercentagePL(investedAmount: Double, currentValue: Double) -> Double {
        guard investedAmount > 0 else { return 0 }
        return ((currentValue - investedAmount) / investedAmount) * 100
    }
    
    // MARK: - Portfolio Level Calculations
    
    /// Calculate total portfolio P&L
    /// - Parameter investments: Array of investments conforming to Investment protocol
    /// - Returns: Tuple with (totalInvested, totalCurrent, absolutePL, percentagePL)
    func calculatePortfolioPL<T: Investment>(_ investments: [T]) -> (
        totalInvested: Double,
        totalCurrent: Double,
        absolutePL: Double,
        percentagePL: Double
    ) {
        let totalInvested = investments.reduce(0) { $0 + $1.investedAmount }
        let totalCurrent = investments.reduce(0) { $0 + $1.currentValue }
        let absolutePL = calculateAbsolutePL(investedAmount: totalInvested, currentValue: totalCurrent)
        let percentagePL = calculatePercentagePL(investedAmount: totalInvested, currentValue: totalCurrent)
        
        return (totalInvested, totalCurrent, absolutePL, percentagePL)
    }
    
    // MARK: - Asset Allocation Calculations
    
    /// Calculate asset allocation percentages
    /// - Parameters:
    ///   - assetValue: Value of specific asset class
    ///   - totalPortfolioValue: Total portfolio value
    /// - Returns: Percentage allocation
    func calculateAssetAllocation(assetValue: Double, totalPortfolioValue: Double) -> Double {
        guard totalPortfolioValue > 0 else { return 0 }
        return (assetValue / totalPortfolioValue) * 100
    }
    
    /// Calculate diversification metrics
    /// - Parameter assetValues: Array of asset class values
    /// - Returns: Dictionary with allocation percentages
    func calculateDiversification(assetValues: [String: Double]) -> [String: Double] {
        let totalValue = assetValues.values.reduce(0, +)
        var allocations: [String: Double] = [:]
        
        for (assetType, value) in assetValues {
            allocations[assetType] = calculateAssetAllocation(assetValue: value, totalPortfolioValue: totalValue)
        }
        
        return allocations
    }
    
    // MARK: - Risk Metrics
    
    /// Calculate portfolio concentration risk (higher values = more concentrated)
    /// - Parameter allocations: Dictionary of asset allocations
    /// - Returns: Concentration score (0-100, lower is better diversified)
    func calculateConcentrationRisk(allocations: [String: Double]) -> Double {
        let percentages = allocations.values.map { $0 / 100 }
        let herfindahlIndex = percentages.reduce(0) { $0 + ($1 * $1) }
        return herfindahlIndex * 100
    }
    
    // MARK: - Performance Metrics
    
    /// Calculate annualized return for a single investment
    /// - Parameters:
    ///   - investedAmount: Initial investment
    ///   - currentValue: Current value
    ///   - investmentDate: Date of investment
    ///   - currentDate: Current date (defaults to today)
    /// - Returns: Annualized return percentage
    func calculateAnnualizedReturn(
        investedAmount: Double,
        currentValue: Double,
        investmentDate: Date,
        currentDate: Date = Date()
    ) -> Double? {
        let years = yearsBetween(investmentDate, currentDate)
        guard years > 0, investedAmount > 0 else { return nil }
        
        let totalReturn = currentValue / investedAmount
        let annualizedReturn = pow(totalReturn, 1.0 / years) - 1.0
        
        return annualizedReturn * 100
    }
    
    /// Calculate compound annual growth rate (CAGR)
    /// - Parameters:
    ///   - beginningValue: Starting value
    ///   - endingValue: Ending value
    ///   - years: Number of years
    /// - Returns: CAGR percentage
    func calculateCAGR(beginningValue: Double, endingValue: Double, years: Double) -> Double? {
        guard years > 0, beginningValue > 0 else { return nil }
        
        let cagr = pow(endingValue / beginningValue, 1.0 / years) - 1.0
        return cagr * 100
    }
    
    // MARK: - Formatting Helpers
    
    /// Format P&L with appropriate color coding
    /// - Parameters:
    ///   - amount: P&L amount
    ///   - includeSign: Whether to include + sign for positive values
    /// - Returns: Formatted string with color indication
    func formatPLWithColor(amount: Double, includeSign: Bool = true) -> (text: String, isPositive: Bool) {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "INR"
        formatter.currencySymbol = "₹"
        
        var formattedText = formatter.string(from: NSNumber(value: abs(amount))) ?? "₹0"
        
        if amount > 0 {
            formattedText = includeSign ? "+\(formattedText)" : formattedText
            return (formattedText, true)
        } else if amount < 0 {
            formattedText = "-\(formattedText)"
            return (formattedText, false)
        } else {
            return (formattedText, true)
        }
    }
    
    /// Format percentage with color coding
    /// - Parameters:
    ///   - percentage: Percentage value
    ///   - includeSign: Whether to include + sign for positive values
    /// - Returns: Formatted percentage string with color indication
    func formatPercentageWithColor(percentage: Double, includeSign: Bool = true) -> (text: String, isPositive: Bool) {
        let sign = percentage > 0 ? (includeSign ? "+" : "") : 
                  percentage < 0 ? "-" : ""
        let formattedText = String(format: "%@%.2f%%", sign, abs(percentage))
        return (formattedText, percentage >= 0)
    }
}

// MARK: - Global Helper Functions
func calculatePL(investedAmount: Double, currentValue: Double) -> (absolute: Double, percentage: Double) {
    let absolute = ProfitLossCalculator.shared.calculateAbsolutePL(investedAmount: investedAmount, currentValue: currentValue)
    let percentage = ProfitLossCalculator.shared.calculatePercentagePL(investedAmount: investedAmount, currentValue: currentValue)
    return (absolute, percentage)
}

func formatPLAmount(_ amount: Double) -> (text: String, isPositive: Bool) {
    return ProfitLossCalculator.shared.formatPLWithColor(amount: amount)
}

func formatPLPercentage(_ percentage: Double) -> (text: String, isPositive: Bool) {
    return ProfitLossCalculator.shared.formatPercentageWithColor(percentage: percentage)
} 