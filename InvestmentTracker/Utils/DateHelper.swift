import Foundation

struct DateHelper {
    static let shared = DateHelper()
    
    private let dateFormatter: DateFormatter
    private let displayFormatter: DateFormatter
    private let shortFormatter: DateFormatter
    
    private init() {
        dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        displayFormatter = DateFormatter()
        displayFormatter.dateStyle = .medium
        displayFormatter.timeStyle = .none
        
        shortFormatter = DateFormatter()
        shortFormatter.dateFormat = "dd/MM/yy"
    }
    
    func format(_ date: Date, style: Style = .display) -> String {
        switch style {
        case .display:
            return displayFormatter.string(from: date)
        case .short:
            return shortFormatter.string(from: date)
        case .iso:
            return dateFormatter.string(from: date)
        }
    }
    
    func daysBetween(_ startDate: Date, _ endDate: Date) -> Int {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: startDate, to: endDate)
        return components.day ?? 0
    }
    
    func monthsBetween(_ startDate: Date, _ endDate: Date) -> Int {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.month], from: startDate, to: endDate)
        return components.month ?? 0
    }
    
    func yearsBetween(_ startDate: Date, _ endDate: Date) -> Double {
        let days = daysBetween(startDate, endDate)
        return Double(days) / 365.25
    }
    
    func isWeekend(_ date: Date) -> Bool {
        let calendar = Calendar.current
        let weekday = calendar.component(.weekday, from: date)
        return weekday == 1 || weekday == 7 // Sunday = 1, Saturday = 7
    }
    
    func addBusinessDays(_ days: Int, to date: Date) -> Date {
        var currentDate = date
        var remainingDays = days
        
        while remainingDays > 0 {
            currentDate = Calendar.current.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
            if !isWeekend(currentDate) {
                remainingDays -= 1
            }
        }
        
        return currentDate
    }
    
    func startOfFinancialYear(for date: Date = Date()) -> Date {
        let calendar = Calendar.current
        let year = calendar.component(.year, from: date)
        let month = calendar.component(.month, from: date)
        
        // Indian financial year starts April 1st
        let fyYear = month >= 4 ? year : year - 1
        
        var components = DateComponents()
        components.year = fyYear
        components.month = 4
        components.day = 1
        
        return calendar.date(from: components) ?? date
    }
    
    func endOfFinancialYear(for date: Date = Date()) -> Date {
        let startOfFY = startOfFinancialYear(for: date)
        return Calendar.current.date(byAdding: DateComponents(year: 1, day: -1), to: startOfFY) ?? date
    }
    
    func timeUntilMaturity(_ maturityDate: Date, from currentDate: Date = Date()) -> (years: Int, months: Int, days: Int)? {
        guard maturityDate > currentDate else { return nil }
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day], from: currentDate, to: maturityDate)
        
        return (
            years: components.year ?? 0,
            months: components.month ?? 0,
            days: components.day ?? 0
        )
    }
    
    enum Style {
        case display
        case short
        case iso
    }
}

// MARK: - Global Helper Functions
func formatDate(_ date: Date, style: DateHelper.Style = .display) -> String {
    return DateHelper.shared.format(date, style: style)
}

func daysBetween(_ startDate: Date, _ endDate: Date) -> Int {
    return DateHelper.shared.daysBetween(startDate, endDate)
}

func yearsBetween(_ startDate: Date, _ endDate: Date) -> Double {
    return DateHelper.shared.yearsBetween(startDate, endDate)
}

func timeUntilMaturity(_ maturityDate: Date) -> String {
    guard let timeLeft = DateHelper.shared.timeUntilMaturity(maturityDate) else {
        return "Matured"
    }
    
    if timeLeft.years > 0 {
        return "\(timeLeft.years)y \(timeLeft.months)m"
    } else if timeLeft.months > 0 {
        return "\(timeLeft.months)m \(timeLeft.days)d"
    } else {
        return "\(timeLeft.days) days"
    }
}

func startOfCurrentFinancialYear() -> Date {
    return DateHelper.shared.startOfFinancialYear()
}

func endOfCurrentFinancialYear() -> Date {
    return DateHelper.shared.endOfFinancialYear()
} 