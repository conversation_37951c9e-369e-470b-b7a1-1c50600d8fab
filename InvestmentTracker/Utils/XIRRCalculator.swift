import Foundation

struct XIRRCalculator {
    static let shared = XIRRCalculator()
    
    private init() {}
    
    /// Calculate XIRR for a series of cash flows
    /// - Parameters:
    ///   - cashFlows: Array of cash flow amounts (negative for investments, positive for returns)
    ///   - dates: Array of dates corresponding to each cash flow
    /// - Returns: XIRR as a percentage, or nil if calculation fails
    func calculateXIRR(cashFlows: [Double], dates: [Date]) -> Double? {
        guard cashFlows.count == dates.count,
              cashFlows.count >= 2 else {
            return nil
        }
        
        // Try Newton-Raphson method first
        if let xirrResult = newtonRaphsonXIRR(cashFlows: cashFlows, dates: dates) {
            return xirrResult
        }
        
        // Fallback to simple annualized return if <PERSON><PERSON><PERSON><PERSON><PERSON> fails
        return calculateSimpleAnnualizedReturn(cashFlows: cashFlows, dates: dates)
    }
    
    /// Calculate XIRR for a single investment
    /// - Parameters:
    ///   - investedAmount: Initial investment (positive value)
    ///   - currentValue: Current value of investment
    ///   - investmentDate: Date of investment
    ///   - currentDate: Current date (defaults to today)
    /// - Returns: Annualized return as percentage
    func calculateXIRR(investedAmount: Double,
                      currentValue: Double,
                      investmentDate: Date,
                      currentDate: Date = Date()) -> Double? {
        
        let cashFlows = [-investedAmount, currentValue]
        let dates = [investmentDate, currentDate]
        
        return calculateXIRR(cashFlows: cashFlows, dates: dates)
    }
    
    // MARK: - Private Helper Methods
    
    private func calculateSimpleAnnualizedReturn(cashFlows: [Double], dates: [Date]) -> Double? {
        guard let firstDate = dates.first,
              let lastDate = dates.last,
              let initialInvestment = cashFlows.first,
              let finalValue = cashFlows.last,
              initialInvestment < 0, finalValue > 0 else {
            return nil
        }
        
        let years = yearsBetween(firstDate, lastDate)
        guard years > 0 else { return nil }
        
        let totalReturn = finalValue / abs(initialInvestment)
        let annualizedReturn = pow(totalReturn, 1.0 / years) - 1.0
        
        return annualizedReturn * 100 // Return as percentage
    }
    
    // MARK: - Newton-Raphson XIRR Implementation
    private func newtonRaphsonXIRR(cashFlows: [Double], dates: [Date], guess: Double = 0.1) -> Double? {
        guard cashFlows.count == dates.count, cashFlows.count >= 2 else { return nil }
        
        let maxIterations = 100
        let tolerance = 1e-6
        var rate = guess
        
        guard let baseDate = dates.first else { return nil }
        
        for _ in 0..<maxIterations {
            var npv = 0.0
            var dnpv = 0.0 // Derivative of NPV
            
            for i in 0..<cashFlows.count {
                let days = daysBetween(baseDate, dates[i])
                let years = Double(days) / 365.25
                let factor = pow(1 + rate, years)
                
                // NPV calculation
                npv += cashFlows[i] / factor
                
                // Derivative calculation
                dnpv -= cashFlows[i] * years / (factor * (1 + rate))
            }
            
            // Check for convergence
            if abs(npv) < tolerance {
                return rate * 100 // Return as percentage
            }
            
            // Newton-Raphson update
            if abs(dnpv) < tolerance {
                break // Avoid division by zero
            }
            
            rate = rate - npv / dnpv
            
            // Prevent unrealistic rates
            if rate < -0.99 || rate > 10.0 {
                break
            }
        }
        
        return nil // Failed to converge
    }
}

// MARK: - Global Helper Functions
func calculateXIRR(investedAmount: Double, currentValue: Double, investmentDate: Date) -> Double? {
    return XIRRCalculator.shared.calculateXIRR(
        investedAmount: investedAmount,
        currentValue: currentValue,
        investmentDate: investmentDate
    )
}

func calculateXIRR(cashFlows: [Double], dates: [Date]) -> Double? {
    return XIRRCalculator.shared.calculateXIRR(cashFlows: cashFlows, dates: dates)
} 