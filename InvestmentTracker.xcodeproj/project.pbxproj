// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		A1B2C3D4E5F60001 /* InvestmentTrackerApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60002 /* InvestmentTrackerApp.swift */; };
		A1B2C3D4E5F60003 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60004 /* ContentView.swift */; };
		A1B2C3D4E5F60005 /* PortfolioView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60006 /* PortfolioView.swift */; };
		A1B2C3D4E5F60007 /* StocksView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60008 /* StocksView.swift */; };
		A1B2C3D4E5F60009 /* MutualFundsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F6000A /* MutualFundsView.swift */; };
		A1B2C3D4E5F6000B /* CryptoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F6000C /* CryptoView.swift */; };
		A1B2C3D4E5F6000F /* Investment.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60010 /* Investment.swift */; };
		A1B2C3D4E5F60011 /* SampleData.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60012 /* SampleData.swift */; };
		A1B2C3D4E5F60013 /* PortfolioViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60014 /* PortfolioViewModel.swift */; };
		A1B2C3D4E5F60015 /* DataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60016 /* DataManager.swift */; };
		A1B2C3D4E5F60017 /* CurrencyFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60018 /* CurrencyFormatter.swift */; };
		A1B2C3D4E5F60019 /* DateHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F6001A /* DateHelper.swift */; };
		A1B2C3D4E5F6001B /* PercentageCalculator.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F6001C /* PercentageCalculator.swift */; };
		A1B2C3D4E5F6001D /* ProfitLossCalculator.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F6001E /* ProfitLossCalculator.swift */; };
		A1B2C3D4E5F6001F /* XIRRCalculator.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60020 /* XIRRCalculator.swift */; };
		A1B2C3D4E5F60035 /* FixedDepositsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60036 /* FixedDepositsView.swift */; };
		A1B2C3D4E5F60037 /* EPFView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60038 /* EPFView.swift */; };
		A1B2C3D4E5F60039 /* NPSView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60040 /* NPSView.swift */; };
		A1B2C3D4E5F60041 /* RSUView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60042 /* RSUView.swift */; };
		A1B2C3D4E5F60043 /* ExchangeRateService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60044 /* ExchangeRateService.swift */; };
		A1B2C3D4E5F60045 /* StockPriceService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60046 /* StockPriceService.swift */; };
		A1B2C3D4E5F60047 /* AddEditStockView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60048 /* AddEditStockView.swift */; };
		A1B2C3D4E5F60049 /* StockPickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60050 /* StockPickerView.swift */; };
		A1B2C3D4E5F60051 /* MFNAVService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60052 /* MFNAVService.swift */; };
		A1B2C3D4E5F60053 /* AddEditMutualFundView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60054 /* AddEditMutualFundView.swift */; };
		A1B2C3D4E5F60055 /* MutualFundPickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60056 /* MutualFundPickerView.swift */; };
		A1B2C3D4E5F60057 /* CryptoPriceService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60058 /* CryptoPriceService.swift */; };
		A1B2C3D4E5F60059 /* AddEditCryptoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60060 /* AddEditCryptoView.swift */; };
		A1B2C3D4E5F60061 /* CryptoPickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60062 /* CryptoPickerView.swift */; };
		A1B2C3D4E5F60063 /* GoldView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60064 /* GoldView.swift */; };
		A1B2C3D4E5F60065 /* AddEditGoldView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60066 /* AddEditGoldView.swift */; };
		A1B2C3D4E5F60067 /* GoldPriceService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60068 /* GoldPriceService.swift */; };
		A1B2C3D4E5F60071 /* CoinDCXService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60072 /* CoinDCXService.swift */; };
		A1B2C3D4E5F60073 /* KeychainManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60074 /* KeychainManager.swift */; };
		A1B2C3D4E5F60075 /* CoinDCXSettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60076 /* CoinDCXSettingsView.swift */; };
		A1B2C3D4E5F60069 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F60070 /* Assets.xcassets */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A1B2C3D4E5F60002 /* InvestmentTrackerApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InvestmentTrackerApp.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60004 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60006 /* PortfolioView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PortfolioView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60008 /* StocksView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StocksView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F6000A /* MutualFundsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MutualFundsView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F6000C /* CryptoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CryptoView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60010 /* Investment.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Investment.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60012 /* SampleData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SampleData.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60014 /* PortfolioViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PortfolioViewModel.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60016 /* DataManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataManager.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60018 /* CurrencyFormatter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CurrencyFormatter.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F6001A /* DateHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DateHelper.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F6001C /* PercentageCalculator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PercentageCalculator.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F6001E /* ProfitLossCalculator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfitLossCalculator.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60020 /* XIRRCalculator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XIRRCalculator.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60021 /* InvestmentTracker.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = InvestmentTracker.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1B2C3D4E5F60036 /* FixedDepositsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FixedDepositsView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60038 /* EPFView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EPFView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60040 /* NPSView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NPSView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60042 /* RSUView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RSUView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60044 /* ExchangeRateService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExchangeRateService.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60046 /* StockPriceService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StockPriceService.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60048 /* AddEditStockView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddEditStockView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60050 /* StockPickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StockPickerView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60052 /* MFNAVService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MFNAVService.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60054 /* AddEditMutualFundView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddEditMutualFundView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60056 /* MutualFundPickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MutualFundPickerView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60058 /* CryptoPriceService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CryptoPriceService.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60060 /* AddEditCryptoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddEditCryptoView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60062 /* CryptoPickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CryptoPickerView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60064 /* GoldView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoldView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60066 /* AddEditGoldView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddEditGoldView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60068 /* GoldPriceService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoldPriceService.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60072 /* CoinDCXService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CoinDCXService.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60074 /* KeychainManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeychainManager.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60076 /* CoinDCXSettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CoinDCXSettingsView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F60070 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A1B2C3D4E5F60022 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A1B2C3D4E5F60023 = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F60024 /* InvestmentTracker */,
				A1B2C3D4E5F60025 /* Products */,
			);
			sourceTree = "<group>";
		};
		A1B2C3D4E5F60024 /* InvestmentTracker */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F60002 /* InvestmentTrackerApp.swift */,
				A1B2C3D4E5F60004 /* ContentView.swift */,
				A1B2C3D4E5F60070 /* Assets.xcassets */,
				A1B2C3D4E5F60026 /* Views */,
				A1B2C3D4E5F60027 /* Models */,
				A1B2C3D4E5F60028 /* ViewModels */,
				A1B2C3D4E5F60029 /* Services */,
				A1B2C3D4E5F6002A /* Utils */,
			);
			path = InvestmentTracker;
			sourceTree = "<group>";
		};
		A1B2C3D4E5F60025 /* Products */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F60021 /* InvestmentTracker.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1B2C3D4E5F60026 /* Views */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F60006 /* PortfolioView.swift */,
				A1B2C3D4E5F60008 /* StocksView.swift */,
				A1B2C3D4E5F60048 /* AddEditStockView.swift */,
				A1B2C3D4E5F60050 /* StockPickerView.swift */,
				A1B2C3D4E5F6000A /* MutualFundsView.swift */,
				A1B2C3D4E5F60054 /* AddEditMutualFundView.swift */,
				A1B2C3D4E5F60056 /* MutualFundPickerView.swift */,
				A1B2C3D4E5F6000C /* CryptoView.swift */,
				A1B2C3D4E5F60060 /* AddEditCryptoView.swift */,
				A1B2C3D4E5F60062 /* CryptoPickerView.swift */,
				A1B2C3D4E5F60076 /* CoinDCXSettingsView.swift */,
				A1B2C3D4E5F60036 /* FixedDepositsView.swift */,
				A1B2C3D4E5F60038 /* EPFView.swift */,
				A1B2C3D4E5F60040 /* NPSView.swift */,
				A1B2C3D4E5F60042 /* RSUView.swift */,
				A1B2C3D4E5F60064 /* GoldView.swift */,
				A1B2C3D4E5F60066 /* AddEditGoldView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A1B2C3D4E5F60027 /* Models */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F60010 /* Investment.swift */,
				A1B2C3D4E5F60012 /* SampleData.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A1B2C3D4E5F60028 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F60014 /* PortfolioViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		A1B2C3D4E5F60029 /* Services */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F60016 /* DataManager.swift */,
				A1B2C3D4E5F60044 /* ExchangeRateService.swift */,
				A1B2C3D4E5F60046 /* StockPriceService.swift */,
				A1B2C3D4E5F60052 /* MFNAVService.swift */,
				A1B2C3D4E5F60058 /* CryptoPriceService.swift */,
				A1B2C3D4E5F60068 /* GoldPriceService.swift */,
				A1B2C3D4E5F60072 /* CoinDCXService.swift */,
				A1B2C3D4E5F60074 /* KeychainManager.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		A1B2C3D4E5F6002A /* Utils */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F60018 /* CurrencyFormatter.swift */,
				A1B2C3D4E5F6001A /* DateHelper.swift */,
				A1B2C3D4E5F6001C /* PercentageCalculator.swift */,
				A1B2C3D4E5F6001E /* ProfitLossCalculator.swift */,
				A1B2C3D4E5F60020 /* XIRRCalculator.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1B2C3D4E5F6002B /* InvestmentTracker */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1B2C3D4E5F6002C /* Build configuration list for PBXNativeTarget "InvestmentTracker" */;
			buildPhases = (
				A1B2C3D4E5F6002D /* Sources */,
				A1B2C3D4E5F60022 /* Frameworks */,
				A1B2C3D4E5F6002E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = InvestmentTracker;
			productName = InvestmentTracker;
			productReference = A1B2C3D4E5F60021 /* InvestmentTracker.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1B2C3D4E5F6002F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					A1B2C3D4E5F6002B = {
						CreatedOnToolsVersion = 16.4;
					};
				};
			};
			buildConfigurationList = A1B2C3D4E5F60030 /* Build configuration list for PBXProject "InvestmentTracker" */;
			compatibilityVersion = "Xcode 15.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A1B2C3D4E5F60023;
			productRefGroup = A1B2C3D4E5F60025 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1B2C3D4E5F6002B /* InvestmentTracker */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A1B2C3D4E5F6002E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1B2C3D4E5F60069 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A1B2C3D4E5F6002D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1B2C3D4E5F60001 /* InvestmentTrackerApp.swift in Sources */,
				A1B2C3D4E5F60003 /* ContentView.swift in Sources */,
				A1B2C3D4E5F60005 /* PortfolioView.swift in Sources */,
				A1B2C3D4E5F60007 /* StocksView.swift in Sources */,
				A1B2C3D4E5F60009 /* MutualFundsView.swift in Sources */,
				A1B2C3D4E5F6000B /* CryptoView.swift in Sources */,
				A1B2C3D4E5F60035 /* FixedDepositsView.swift in Sources */,
				A1B2C3D4E5F60037 /* EPFView.swift in Sources */,
				A1B2C3D4E5F60039 /* NPSView.swift in Sources */,
				A1B2C3D4E5F60041 /* RSUView.swift in Sources */,
				A1B2C3D4E5F60063 /* GoldView.swift in Sources */,
				A1B2C3D4E5F60065 /* AddEditGoldView.swift in Sources */,
				A1B2C3D4E5F6000F /* Investment.swift in Sources */,
				A1B2C3D4E5F60011 /* SampleData.swift in Sources */,
				A1B2C3D4E5F60013 /* PortfolioViewModel.swift in Sources */,
				A1B2C3D4E5F60015 /* DataManager.swift in Sources */,
				A1B2C3D4E5F60043 /* ExchangeRateService.swift in Sources */,
				A1B2C3D4E5F60045 /* StockPriceService.swift in Sources */,
				A1B2C3D4E5F60067 /* GoldPriceService.swift in Sources */,
				A1B2C3D4E5F60071 /* CoinDCXService.swift in Sources */,
				A1B2C3D4E5F60073 /* KeychainManager.swift in Sources */,
				A1B2C3D4E5F60047 /* AddEditStockView.swift in Sources */,
				A1B2C3D4E5F60049 /* StockPickerView.swift in Sources */,
				A1B2C3D4E5F60051 /* MFNAVService.swift in Sources */,
				A1B2C3D4E5F60053 /* AddEditMutualFundView.swift in Sources */,
				A1B2C3D4E5F60055 /* MutualFundPickerView.swift in Sources */,
				A1B2C3D4E5F60057 /* CryptoPriceService.swift in Sources */,
				A1B2C3D4E5F60059 /* AddEditCryptoView.swift in Sources */,
				A1B2C3D4E5F60061 /* CryptoPickerView.swift in Sources */,
				A1B2C3D4E5F60075 /* CoinDCXSettingsView.swift in Sources */,
				A1B2C3D4E5F60017 /* CurrencyFormatter.swift in Sources */,
				A1B2C3D4E5F60019 /* DateHelper.swift in Sources */,
				A1B2C3D4E5F6001B /* PercentageCalculator.swift in Sources */,
				A1B2C3D4E5F6001D /* ProfitLossCalculator.swift in Sources */,
				A1B2C3D4E5F6001F /* XIRRCalculator.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A1B2C3D4E5F60031 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A1B2C3D4E5F60032 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A1B2C3D4E5F60033 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PH95U76N8K;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = Nilai;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.personal.InvestmentTracker;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A1B2C3D4E5F60034 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PH95U76N8K;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = Nilai;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.personal.InvestmentTracker;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A1B2C3D4E5F6002C /* Build configuration list for PBXNativeTarget "InvestmentTracker" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1B2C3D4E5F60033 /* Debug */,
				A1B2C3D4E5F60034 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1B2C3D4E5F60030 /* Build configuration list for PBXProject "InvestmentTracker" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1B2C3D4E5F60031 /* Debug */,
				A1B2C3D4E5F60032 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A1B2C3D4E5F6002F /* Project object */;
}
