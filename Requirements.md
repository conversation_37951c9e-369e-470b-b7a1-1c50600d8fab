## Goal :
Need an ios app as one place to view all the personal investments and track the p&l and overall split across the different asset classes.(stocks, mutual funds, crypto, FDs, EPF, NPS, ESOP, Gold)

## Stocks
- Should be able to fetch the current price for the stocks in the current portfolio from the broker's API or google finance
- Should be able to show current p&l for each stock and overall portfolio and XIRR
- For now, should be able to manually enter the current portfolio details via some report upload or enter them manually one by one
    
## Mutual Funds

- Should be able to fetch the current price of the mutual funds from the broker's API or google finance
- Should be able to show current p&l for each mutual fund and overall portfolio and XIRR
- For now, should be able to manually enter the current portfolio details via some report upload and or enter them manually one by one

## Crypto

- Should be able to fetch the current price of the crypto from the broker's API (especially from the invested broker as in India the price is different for different brokers)
- Should be able to show current p&l for each crypto and overall portfolio and XIRR
- For now, should be able to manually enter the current portfolio details via some report upload and or enter them manually one by one

## FDs
- Should allow to enter manually the bank, the fd amount, start date, end date and interest rate
- Should be able to show current p&l for each FD and overall portfolio and XIRR

## EPF
- Should be able to show invested amount and the current amount and the XIRR
- For now, should be able to manually enter the current invested amount now and then (preferably monthly or quarterly)
- Not sure how to derive the profit or returns as the interest split is not specified clearly. Of course there is no loss since its epf

## NPS
- Should be able to show invested amount and the current amount and the XIRR
- For now, should be able to manually enter the current invested amount now and then (preferably monthly or quarterly)
- Manually I will be adding the invested and current. So should be able to calculate the XIRR

## ESOP/RSUs
- For now, only RSU support needed, should be able to capture manually the no. of stocks, unit value and the total value

## Gold
- Should be able to add manuall entry of gold rate and gold grams historically and show the current value and p&l
- note : unlike other instruments like mf, equity where i get updated full protfolio and avg value, for gold, i have individual purchase history. accept the individual entries but show the agg avg value and p&l

## Main Use Cases
- All of the values should be in INR
- Should allow add, edit and delete wherever possible
- should support granular level and overall p&l and XIRR
- Automatically fetch market value via APIs
- Show overall portfolio with diversification breakdown

## Tech stack and background
 - Im mainly a web app backend developer. So Ios development, dev testing, local installation is not my forte. So will need help with that.
 - Not sure if I can use personally in my phone without publishing it to app store.

Future scope:
- For equity, Should be able to integrate with mutliple brokers (Zerodha and shoonya for now) through secret key and fetch the current portfolio
- For mutual funds, Similar to Stocks, should be able to integrate with multiple brokers and fetch the current portfolio
- For crypto, Should be able to integrate with multiple brokers (Binance, WazirX, CoinDCX) and fetch the current portfolio
- For EPF, Should allow to automatically fetch the epf passbook or atleast balance if possible via API with UAN Number. Need to check the feasibility
- For NPS, Should allow to automatically fetch the nps account details or atleast balance if possible via API with nominee number. Need to check the feasibility
- For all of these instruments, a history of investments will be needed to be stored as transaction history
- Should support ESOPs as well
- Track historical performance over time (line charts) 
- Set and monitor investment goals
- User authentication (single-user, personal project)
- create a common backend server and fetch/update data to that. this will be helpful to create a web app as well
- add a toggle to show the p&l post tax. for ex, fd has 30% slab, equity short term has 20%, crypto 30, rsu 30. so just with the toggle i can see the p&l post tax
- add a toggle to hide few instruments from the portfolio
